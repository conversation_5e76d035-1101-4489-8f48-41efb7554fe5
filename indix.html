<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Elashrafy AI Model - نموذج الذكاء الاصطناعي الأشرافي للمحادثات الذكية">
    <meta name="keywords" content="ذكاء اصطناعي, محادثة, AI, chatbot, Arabic AI">
    <meta name="author" content="Elashrafy AI">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="EAM - Elashrafy AI Model">
    <meta property="og:description" content="نموذج الذكاء الاصطناعي الأشرافي للمحادثات الذكية">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://al-ishrafi-accounting-2025.web.app">
    <meta name="theme-color" content="#2563eb">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
    <!-- Manifest will be injected only on http/https to avoid CORS errors on file:// -->
    <script>
      (function(){
        const isHttp = location.protocol === 'http:' || location.protocol === 'https:';
        if (isHttp) {
          const link = document.createElement('link');
          link.rel = 'manifest';
          link.href = '/manifest.json';
          document.head.appendChild(link);
        }
      })();
    </script>
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 192'><rect width='192' height='192' fill='%232563eb' rx='24'/><text x='96' y='130' font-size='100' text-anchor='middle' fill='white'>🤖</text></svg>">
    <title>EAM - Elashrafy AI Model</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Supabase SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet" />
    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, orderBy, limit } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyByyKowJQxpRkKi5A-NVSDQ9SVbve_YXH8",
            authDomain: "al-ishrafi-accounting-2025.firebaseapp.com",
            projectId: "al-ishrafi-accounting-2025",
            storageBucket: "al-ishrafi-accounting-2025.firebasestorage.app",
            messagingSenderId: "************",
            appId: "1:************:web:e10c2aab80cdc226d9989d",
            measurementId: "G-WY1YB5WJ9T"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Make Firebase available globally (disabled temporarily)
        // window.firebaseDB = db;
        // window.firebaseCollections = { collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, orderBy, limit };
        window.firebaseDB = null; // Disable Firebase to avoid permission errors

        // Initialize Supabase (disabled temporarily due to API key issues)
        // const supabaseUrl = 'https://wimvqggxgvwuwrivzjgm.supabase.co';
        // const supabaseKey = 'INVALID_KEY';
        // const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Disable Supabase for now, use localStorage only
        window.supabaseClient = null;

        // Make Supabase available globally (disabled)
        // window.supabaseClient = supabase;
        console.log('Supabase تم تعطيله مؤقتاً - استخدام localStorage فقط');
    </script>
    <style>
        :root {
            /* Dark Theme - ChatGPT Style */
            --primary: #2563eb; /* Blue accent */
            --primary-light: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary: #2f2f2f; /* Dark surfaces */
            --bg-main: #212121; /* Main dark background */
            --bg-light: #2f2f2f; /* Lighter dark surfaces */
            --bg-dark: #171717; /* Darker surfaces */
            --text: #ffffff; /* White text */
            --text-secondary: #b3b3b3; /* Light gray text */
            --text-light: #8e8ea0; /* Muted text */
            --border: #444444; /* Dark borders */
            --border-light: #333333; /* Subtle borders */
            --user-msg: #2f2f2f; /* User message background */
            --ai-msg: #1a1a1a; /* AI message background */
            --input-bg: #2f2f2f;
            --input-border: #444444;
            --shadow-light: rgba(0, 0, 0, 0.3);
            --shadow-medium: rgba(0, 0, 0, 0.4);
            --shadow-strong: rgba(0, 0, 0, 0.6);
            --success: #10a37f;
            --danger: #ff4444;
            --sidebar-width: 72px;
            --sidebar-expanded-width: 280px;

            /* Animation variables */
            --transition-fast: 0.2s ease;
            --transition-medium: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Cairo', sans-serif;
        }

        body {
            background: radial-gradient(ellipse at center, #2a2a2a 0%, #1a1a1a 50%, #0f0f0f 100%);
            color: var(--text);
            height: 100vh;
            display: flex;
            overflow: hidden;
            font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            position: relative;
        }

        /* Subtle background pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(0,0,0,0.01) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(0,0,0,0.01) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .app-container {
            display: flex;
            width: 100%;
            height: 100%;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--bg-dark);
            color: var(--text);
            display: flex;
            flex-direction: column;
            align-items: center;
            overflow: hidden;
            transition: all 0.3s ease;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            transform: translateX(0);
            border-right: 1px solid var(--border-light);
            padding: 16px 0;
        }

        /* Hide sidebar by default on mobile */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
        }

        .sidebar.expanded {
            width: var(--sidebar-expanded-width);
            align-items: flex-start;
            padding: 16px;
        }

        /* Show sidebar when open */
        .sidebar.open {
            transform: translateX(0);
        }

        /* Adjust main content when sidebar is open on desktop */
        .main-content {
            transition: margin-left var(--transition-medium);
            margin-left: var(--sidebar-width); /* افتراضياً مع الشريط الجانبي */
        }

        @media (min-width: 769px) {
            .sidebar.open ~ .main-content {
                margin-left: var(--sidebar-width);
            }

            .sidebar.open.expanded ~ .main-content {
                margin-left: var(--sidebar-expanded-width);
            }

            .sidebar:not(.open) ~ .main-content {
                margin-left: 0;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                margin-left: 0 !important;
            }
        }

        /* Mobile sidebar overlay */
        @media (max-width: 768px) {
            .sidebar {
                box-shadow: 2px 0 10px var(--shadow-medium);
            }
            
            .sidebar.expanded {
                width: 250px;
            }
            
            .expand-btn {
                display: none;
            }
        }

        .sidebar-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
            width: 100%;
        }

        .sidebar.expanded .sidebar-header {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .sidebar-brand {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .sidebar-brand-text {
            opacity: 0;
            width: 0;
            margin-left: 0;
            font-size: 20px;
            font-weight: 700;
            color: var(--text);
            transition: all 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar.expanded .sidebar-brand-text {
            opacity: 1;
            width: auto;
            margin-left: 12px;
        }

        .expand-btn {
            width: 32px;
            height: 32px;
            background: var(--bg-light);
            border: 1px solid var(--border);
            color: var(--text-secondary);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .expand-btn:hover {
            background: var(--bg-main);
            color: var(--text);
        }

        .sidebar-brand:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
        }

        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;
            align-items: center;
            width: 100%;
            padding: 0 8px;
        }

        .sidebar.expanded .sidebar-nav {
            align-items: stretch;
            padding: 0;
        }

        .sidebar-nav-item {
            width: 44px;
            height: 44px;
            background: transparent;
            border: none;
            color: var(--text-secondary);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            position: relative;
            text-align: right;
        }

        .sidebar.expanded .sidebar-nav-item {
            width: 100%;
            justify-content: flex-start;
            padding: 0 16px;
            gap: 12px;
        }

        .sidebar-nav-item-text {
            opacity: 0;
            width: 0;
            transition: all 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
            font-size: 14px;
            font-weight: 500;
        }

        .sidebar.expanded .sidebar-nav-item-text {
            opacity: 1;
            width: auto;
        }

        .sidebar.expanded .sidebar-nav-item .tooltip {
            display: none;
        }

        .sidebar.expanded .user-profile .tooltip {
            display: none;
        }

        /* RTL adjustments for sidebar */
        [dir="rtl"] .sidebar-nav-item .tooltip,
        [dir="rtl"] .user-profile .tooltip {
            left: auto;
            right: 60px;
        }

        [dir="rtl"] .sidebar.expanded .sidebar-brand-text {
            margin-left: 0;
            margin-right: 12px;
        }

        /* Sidebar Content Sections */
        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 8px;
            margin-top: 16px;
            display: none;
        }

        .sidebar.expanded .sidebar-content {
            display: block;
        }

        .sidebar-section {
            padding: 16px;
            border-radius: 8px;
            background: var(--bg-light);
            margin-bottom: 16px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border);
        }

        .section-header h3 {
            font-size: 14px;
            font-weight: 600;
            color: var(--text);
            margin: 0;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border);
            border-radius: 6px;
            background: var(--bg-main);
            color: var(--text);
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--primary);
        }

        .search-results {
            margin-top: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .search-result-item {
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            margin-bottom: 4px;
            background: var(--bg-main);
            border: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .search-result-item:hover {
            border-color: var(--primary);
            background: rgba(37, 99, 235, 0.1);
        }

        .search-result-title {
            font-size: 13px;
            font-weight: 500;
            color: var(--text);
            margin-bottom: 4px;
        }

        .search-result-preview {
            font-size: 12px;
            color: var(--text-secondary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chat-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .chat-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 6px;
            background: var(--bg-main);
            border: 1px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .chat-item:hover {
            border-color: var(--primary);
            background: rgba(37, 99, 235, 0.1);
        }

        .chat-item.active {
            background: var(--primary);
            color: white;
        }

        .chat-item-icon {
            width: 32px;
            height: 32px;
            background: var(--primary);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-left: 12px;
        }

        .chat-item-content {
            flex: 1;
            min-width: 0;
        }

        .chat-item-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chat-item-preview {
            font-size: 12px;
            color: var(--text-secondary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chat-item-actions {
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .chat-item:hover .chat-item-actions {
            opacity: 1;
        }

        .chat-action-btn {
            width: 24px;
            height: 24px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .chat-action-btn:hover {
            background: var(--danger);
            color: white;
        }

        .bookmark-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .bookmark-item {
            display: flex;
            align-items: flex-start;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 8px;
            background: var(--bg-main);
            border: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .bookmark-item:hover {
            border-color: var(--primary);
            background: rgba(37, 99, 235, 0.1);
        }

        .settings-content {
            padding: 16px 0;
        }

        .setting-group {
            margin-bottom: 16px;
        }

        .setting-group label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text);
            margin-bottom: 8px;
        }

        .setting-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border);
            border-radius: 6px;
            background: var(--bg-main);
            color: var(--text);
            font-size: 14px;
            outline: none;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            color: var(--text);
        }

        .checkbox-label input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .clear-history-btn,
        .add-bookmark-btn {
            width: 32px;
            height: 32px;
            border: 1px solid var(--border);
            background: var(--bg-main);
            color: var(--text-secondary);
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .clear-history-btn:hover {
            background: var(--danger);
            color: white;
            border-color: var(--danger);
        }

        .add-bookmark-btn:hover {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .sidebar-nav-item:hover {
            background: var(--bg-light);
            color: var(--text);
            transform: scale(1.05);
        }

        .sidebar-nav-item.active {
            background: var(--primary);
            color: white;
        }

        .sidebar-nav-item .tooltip {
            position: absolute;
            left: 60px;
            background: var(--bg-dark);
            color: var(--text);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            border: 1px solid var(--border);
            z-index: 1001;
        }

        .sidebar-nav-item:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 16px;
        }

        .user-profile {
            width: 44px;
            height: 44px;
            background: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .sidebar.expanded .user-profile {
            width: 100%;
            border-radius: 8px;
            justify-content: flex-start;
            padding: 0 16px;
            gap: 12px;
        }

        .user-avatar-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .user-info-expanded {
            opacity: 0;
            width: 0;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .sidebar.expanded .user-info-expanded {
            opacity: 1;
            width: auto;
        }

        .user-name {
            font-size: 14px;
            font-weight: 500;
            color: white;
        }

        .user-email {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .user-profile:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
        }

        .user-profile .tooltip {
            position: absolute;
            left: 60px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--bg-dark);
            color: var(--text);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            border: 1px solid var(--border);
            z-index: 1001;
        }

        .user-profile:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }

        .chat-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 16px;
        }

        .new-chat-btn {
            flex: 1;
            background: var(--bg-light);
            border: 1px solid var(--border);
            color: var(--text);
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .new-chat-btn:hover {
            background: var(--bg-main);
            border-color: var(--text-light);
        }

        .category-btn {
            background-color: var(--bg-light);
            border: 1px solid var(--border);
            color: var(--text);
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
            position: relative;
        }

        .category-btn:hover {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .category-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            min-width: 200px;
            display: none;
            flex-direction: column;
            margin-top: 5px;
        }

        .category-dropdown.show {
            display: flex;
        }

        .category-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: background-color 0.2s, transform 0.1s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            border-radius: 8px;
        }
        .category-item:hover { transform: translateY(-1px); }

        .category-item:hover {
            background-color: var(--bg-light);
        }

        .category-item:first-child {
            border-radius: 8px 8px 0 0;
        }

        .category-item:last-child {
            border-radius: 0 0 8px 8px;
        }

        .chat-history {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .chat-history-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 12px;
            padding: 0 8px;
        }

        .chat-item {
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
        }

        .chat-item:hover {
            background-color: var(--bg-light);
        }

        .chat-item:hover .delete-chat {
            opacity: 1;
        }

        .chat-item.active {
            background-color: rgba(37, 99, 235, 0.1);
        }

        .chat-item-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: var(--bg-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
        }

        .chat-item-content {
            flex: 1;
            overflow: hidden;
        }

        .chat-item-title {
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chat-item-date {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .delete-chat {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--danger);
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
            padding: 5px;
            z-index: 10;
        }

        .delete-chat:hover {
            opacity: 1;
            background-color: rgba(239, 68, 68, 0.1);
            border-radius: 4px;
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid var(--border);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .user-profile:hover {
            background-color: var(--bg-light);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
        }

        .user-email {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: transparent;
        }

        .header {
            background: var(--bg-dark);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-light);
            position: sticky;
            top: 0;
            z-index: 10;
            transition: all var(--transition-medium);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .logo-text {
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .model-info {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: -4px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .beta-icon {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 20px;
            color: white;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
            transition: all 0.3s ease;
        }

        .beta-icon:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
        }

        .beta-icon i {
            font-size: 14px;
        }

        .header-btn {
            background: var(--bg-light);
            border: 1px solid var(--border);
            color: var(--text-secondary);
            width: 44px;
            height: 44px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all var(--transition-medium);
            position: relative;
            overflow: hidden;
        }

        .header-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: var(--text);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .header-btn:hover {
            border-color: var(--text);
            color: var(--secondary);
            transform: scale(1.1);
        }

        .header-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .header-btn i {
            position: relative;
        /* Model switcher styles (global) */
        .model-switcher{ display:flex; gap:8px; align-items:center; margin-inline-start: 12px; }
        .model-pill{ display:flex; align-items:center; gap:8px; padding:6px 10px; border-radius:999px; border:1px solid var(--border); background:var(--bg-light); color:var(--text-secondary); cursor:pointer; font-size:12px; }
        .model-pill .cap{ font-size:10px; opacity:.8; }
        .model-pill.active{ border-color: var(--secondary); color: var(--text); box-shadow: inset 0 0 0 2px rgba(255,255,255,0.06); }

            z-index: 1;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            background: var(--bg-main);
            position: relative;
            transition: all 0.4s ease;
        }

        /* Initial welcome state - centered */
        .chat-container.welcome-mode {
            justify-content: center;
            align-items: center;
            padding: 0 20px 140px;
            min-height: 100vh;
            opacity: 1 !important;
            transform: translateY(0) scale(1) !important;
        }

        .chat-container.welcome-mode .welcome-screen {
            display: flex !important;
            opacity: 1 !important;
            transform: translateY(0) !important;
        }

        /* Model switcher styles */
        .model-switcher{ display:flex; gap:8px; align-items:center; margin-inline-start: 16px; }
        .model-pill{
            position:relative; display:flex; align-items:center; gap:8px;
            padding:8px 12px; border-radius:999px; border:1px solid var(--border);
            background:var(--bg-light); color:var(--text-secondary); cursor:pointer;
            font-size:12px; transition: all 0.3s ease;
            overflow: hidden;
        }

        /* حدود خضراء دوارة لأزرار النماذج */
        .model-pill::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: conic-gradient(
                from 0deg,
                transparent,
                #10a37f,
                transparent,
                #22e5c7,
                transparent,
                #1a9f7a,
                transparent
            );
            border-radius: 999px;
            z-index: -1;
            animation: rotateModelBorder 6s linear infinite;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .model-pill:hover::before{ 
            opacity: 0.6;
        }

        .model-pill:hover{ 
            border-color: var(--primary); 
            background: var(--bg-main);
            transform: scale(1.05);
        }
        
        .model-pill .cap{ font-size:10px; opacity:.8; }
        
        .model-pill.active{
            border-color: var(--primary); color: var(--text);
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white; box-shadow: 0 4px 12px rgba(16, 163, 127, 0.4);
        }

        .model-pill.active::before {
            opacity: 1;
            animation: rotateModelBorder 3s linear infinite;
        }

        @keyframes rotateModelBorder {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        .model-pill.active .cap{ opacity: 1; }
        .model-pill .badge-new{
            position:absolute; top:-8px; inset-inline-end:-6px;
        }

        /* Code highlighting and display (Qwen3-Coder enhancement) */
        .code-block {
            position: relative;
            margin: 16px 0;
            border-radius: 12px;
            overflow: hidden;
            background: #0d1117;
            border: 1px solid #30363d;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #161b22;
            border-bottom: 1px solid #30363d;
            font-size: 13px;
            color: #f0f6fc;
        }

        .code-language {
            font-weight: 600;
            color: #58a6ff;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .code-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .copy-code-btn, .preview-code-btn {
            background: #21262d;
            border: 1px solid #30363d;
            color: #f0f6fc;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .copy-code-btn:hover, .preview-code-btn:hover {
            background: #30363d;
            border-color: #58a6ff;
            color: #58a6ff;
        }

        .copy-code-btn.copied {
            background: linear-gradient(135deg, #2ea043 0%, #238636 100%) !important;
            border-color: #2ea043;
            color: white;
            transform: scale(0.95);
            box-shadow: 0 4px 12px rgba(35, 134, 54, 0.4) !important;
        }

        .preview-code-btn {
            background: #7c3aed;
            border-color: #8b5cf6;
        }

        .preview-code-btn:hover {
            background: #8b5cf6;
            border-color: #a78bfa;
        }

        .code-content {
            padding: 0;
            margin: 0;
            overflow-x: auto;
            background: #0d1117;
        }

        .code-content pre {
            margin: 0;
            padding: 20px;
            background: transparent;
            color: #f0f6fc;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .code-content code {
            font-family: inherit;
            font-size: inherit;
            background: transparent;
            padding: 0;
            border: none;
        }

        /* Enhanced typing indicator for Qwen */
        .message.ai.qwen-typing {
            border-left: 3px solid #58a6ff;
        }

        .message.ai.qwen-typing .avatar {
            background: linear-gradient(135deg, #58a6ff 0%, #d2a8ff 100%);
            color: white;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: var(--bg-light);
            border-radius: 12px;
            margin: 8px 0;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--text-secondary);
            animation: typing-bounce 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        .qwen-typing .typing-dot {
            background: #58a6ff;
            animation: qwen-typing-bounce 1.2s infinite ease-in-out;
        }

        @keyframes typing-bounce {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1.2); opacity: 1; }
        }

        @keyframes qwen-typing-bounce {
            0%, 80%, 100% { transform: scale(0.6) translateY(0); opacity: 0.4; }
            40% { transform: scale(1.4) translateY(-8px); opacity: 1; }
        }

        .typing-status {
            font-size: 11px;
            color: #58a6ff;
            font-weight: 500;
            margin-inline-start: 8px;
        }

        /* Qwen response styling */
        .message.ai.qwen-response {
            border-left: 3px solid #58a6ff;
            background: linear-gradient(135deg, rgba(88, 166, 255, 0.05) 0%, rgba(210, 168, 255, 0.05) 100%);
        }

        .message.ai.qwen-response .avatar {
            background: linear-gradient(135deg, #58a6ff 0%, #d2a8ff 100%);
            color: white;
            font-weight: bold;
        }
        /* Inline code styling */
        .inline-code {
            background: #161b22;
            color: #79c0ff;
            padding: 3px 6px;
            border-radius: 6px;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            border: 1px solid #30363d;
            font-weight: 500;
        }

        /* Enhanced Code Block Styles */
        .enhanced-code-block {
            position: relative;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4) !important;
        }

        .enhanced-code-block:hover {
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.5) !important;
            transform: translateY(-2px);
        }

        .enhanced-code-block .copy-code-btn:hover {
            background: linear-gradient(135deg, #2ea043 0%, #238636 100%) !important;
            transform: scale(1.05) !important;
            box-shadow: 0 6px 16px rgba(46, 160, 67, 0.4) !important;
        }

        .enhanced-code-block .code-toggle-btn:hover {
            background: #30363d !important;
            color: #f0f6fc !important;
            border-color: #58a6ff !important;
        }

        .enhanced-code-block .code-line-numbers {
            font-size: 12px !important;
            width: 50px !important;
            padding-right: 15px !important;
            border-right: 2px solid #30363d !important;
        }

        .qwen-response .enhanced-code-block {
            animation: slideInCode 0.6s ease-out;
        }

        @keyframes slideInCode {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Qwen3-Coder Code Block Styles */
        .qwen-code-block {
            animation: slideInUp 0.4s ease-out;
            border: 1px solid #30363d !important;
            background: linear-gradient(145deg, #0d1117 0%, #161b22 100%) !important;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .qwen-code-block .copy-code-btn:hover {
            background: linear-gradient(135deg, #2ea043 0%, #238636 100%) !important;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(35, 134, 54, 0.4) !important;
        }

        .qwen-code-block .code-toggle-btn:hover {
            background: rgba(48, 54, 61, 0.8) !important;
            color: #f0f6fc !important;
            transform: translateY(-1px);
        }

        .qwen-code-block .code-content {
            scrollbar-width: thin;
            scrollbar-color: #30363d #0d1117;
        }

        .qwen-code-block .code-content::-webkit-scrollbar {
            height: 8px;
        }

        .qwen-code-block .code-content::-webkit-scrollbar-track {
            background: #0d1117;
        }

        .qwen-code-block .code-content::-webkit-scrollbar-thumb {
            background: #30363d;
            border-radius: 4px;
        }

        .qwen-code-block .code-content::-webkit-scrollbar-thumb:hover {
            background: #484f58;
        }

        /* Enhanced syntax highlighting for better readability */
        .qwen-code-block code[class*="language-"] {
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            font-feature-settings: "liga" 1, "calt" 1;
        }

        /* Line numbers enhancement */
        .qwen-code-block .line-number {
            transition: color 0.2s ease;
        }

        .qwen-code-block .code-content:hover .line-number {
            color: #8b949e;
        }

        /* Language icon styling */
        .qwen-code-block .language-icon {
            transition: all 0.3s ease;
        }

        .qwen-code-block:hover .language-icon {
            transform: scale(1.1);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .enhanced-code-block .code-header {
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
            }
            
            .enhanced-code-block .code-line-numbers {
                display: none;
            }
            
            .enhanced-code-block pre {
                padding-left: 20px !important;
            }
        }


        /* Enhanced Prism.js theme - GitHub Dark style */
        .code-content .token.comment,
        .code-content .token.prolog,
        .code-content .token.doctype,
        .code-content .token.cdata { color: #8b949e; font-style: italic; }

        .code-content .token.punctuation { color: #f0f6fc; }

        .code-content .token.property,
        .code-content .token.tag,
        .code-content .token.boolean,
        .code-content .token.number,
        .code-content .token.constant,
        .code-content .token.symbol,
        .code-content .token.deleted { color: #79c0ff; }

        .code-content .token.selector,
        .code-content .token.attr-name,
        .code-content .token.string,
        .code-content .token.char,
        .code-content .token.builtin,
        .code-content .token.inserted { color: #a5d6ff; }

        .code-content .token.operator,
        .code-content .token.entity,
        .code-content .token.url,
        .code-content .language-css .token.string,
        .code-content .style .token.string { color: #ff7b72; }

        .code-content .token.atrule,
        .code-content .token.attr-value,
        .code-content .token.keyword { color: #ff7b72; font-weight: 600; }

        .code-content .token.function,
        .code-content .token.class-name { color: #d2a8ff; font-weight: 600; }

        .code-content .token.regex,
        .code-content .token.important,
        .code-content .token.variable { color: #ffa657; }

        .code-content .token.important,
        .code-content .token.bold { font-weight: bold; }

        .code-content .token.italic { font-style: italic; }

        /* Code folding/expansion styles */
        .code-toggle-btn {
            background: #21262d;
            border: 1px solid #30363d;
            color: #f0f6fc;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            margin-left: 8px;
        }

        .code-toggle-btn:hover {
            background: #30363d;
            color: #58a6ff;
        }

        .code-content.collapsed {
            max-height: 100px;
            overflow: hidden;
            position: relative;
        }

        .code-content.collapsed::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(transparent, #0d1117);
            pointer-events: none;
        }

        /* Chat mode - normal flow */
        .chat-container.chat-mode {
            padding: 20px;
            gap: 16px;
            justify-content: flex-start;
        }

        /* Messages area for chat mode */
        .messages-area {
            display: none;
            flex-direction: column;
            gap: 16px;
            padding-bottom: 20px;
        }

        .chat-container.chat-mode .messages-area {
            display: flex;
        }

        .message {
            display: flex;
            gap: 12px;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .avatar.user {
            background-color: var(--primary);
            color: white;
        }

        .avatar.ai {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
        }

        .message-content {
            max-width: 80%;
            padding: 14px 18px;
            border-radius: 18px;
            position: relative;
            line-height: 1.6;
            box-shadow: 0 2px 8px var(--shadow-light);
        }

        .message.user .message-content {
            background-color: var(--user-msg);
            color: var(--text);
            border-bottom-right-radius: 4px;
        }

        .message.ai .message-content {
            background-color: var(--ai-msg);
            border-bottom-left-radius: 4px;
        }

        .message-image {
            max-width: 100%;
            border-radius: 8px;
            margin-top: 8px;
        }

        .message-actions {
            position: absolute;
            top: 8px;
            left: 8px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .message.ai:hover .message-actions {
            opacity: 1;
        }

        .copy-btn {
            background: rgba(255, 255, 255, 0.8);
            border: none;
            color: var(--text-secondary);
            width: 28px;
            height: 28px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .copy-btn:hover {
            background: white;
            color: var(--text);
        }

        .copy-btn.copied {
            background: var(--success);
            color: white;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 14px 18px;
            background-color: var(--ai-msg);
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            width: fit-content;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--text-secondary);
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-4px);
                opacity: 1;
            }
        }

        /* Smooth transitions for mode switching */
        .chat-container.initial-state .empty-state {
            opacity: 1;
            transform: translateY(0);
        }

        .chat-container.chat-mode .empty-state {
            opacity: 0;
            transform: translateY(-20px);
            pointer-events: none;
        }

        /* Sparkle animation */
        @keyframes sparkle {
            0% {
                transform: translate(-50%, -50%) rotate(var(--angle)) translateX(0) scale(0);
                opacity: 1;
            }
            50% {
                opacity: 1;
                transform: translate(-50%, -50%) rotate(var(--angle)) translateX(60px) scale(1);
            }
            100% {
                transform: translate(-50%, -50%) rotate(var(--angle)) translateX(120px) scale(0);
                opacity: 0;
            }
        }

        /* Pulse effect for interactive elements */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .suggestion:active {
            animation: pulse 0.3s ease;
        }

        .send-btn.enabled:active {
            animation: pulse 0.2s ease;
        }

        /* Glowing effect for focus states */
        .input-form:focus-within {
            position: relative;
        }

        .input-form:focus-within::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--text), var(--text-secondary), var(--text));
            border-radius: 18px;
            z-index: -1;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { opacity: 0.5; }
            to { opacity: 0.8; }
        }

        /* Typing indicator */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 12px 18px;
            background-color: var(--ai-msg);
            border-radius: 18px;
            border-bottom-left-radius: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--text-secondary);
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        .input-container {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: transparent;
            padding: 24px;
            display: flex;
            justify-content: center;
            z-index: 100;
        }

        .input-form {
            display: flex;
            align-items: center;
            background: var(--bg-light);
            border: 1px solid var(--border);
            border-radius: 24px;
            padding: 12px 16px;
            max-width: 768px;
            width: 100%;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .input-form:focus-within {
            border-color: var(--border);
            box-shadow: 0 4px 32px rgba(0, 0, 0, 0.15);
        }

        .input-field {
            flex: 1;
            background: transparent;
            border: none;
            outline: none;
            color: var(--text);
            font-size: 16px;
            resize: none;
            min-height: 24px;
            max-height: 120px;
            font-family: inherit;
        }

        .input-field::placeholder {
            color: var(--text-light);
        }

        .input-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 8px;
        }

        .action-btn {
            background: transparent;
            border: none;
            color: var(--text-light);
            width: 32px;
            height: 32px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        /* ألوان خضراء متحركة حول أيقونات الإدخال */
        .action-btn::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: conic-gradient(
                from 0deg,
                transparent,
                #10a37f,
                transparent,
                #22e5c7,
                transparent,
                #1a9f7a,
                transparent
            );
            border-radius: 8px;
            z-index: -1;
            animation: rotateIconBorder 3s linear infinite;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .action-btn:hover::before {
            opacity: 1;
        }

        .action-btn:hover {
            background-color: var(--bg-dark);
            color: var(--primary);
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(16, 163, 127, 0.3);
        }

        /* تأثير نبضة خفيفة للأيقونات النشطة */
        .action-btn:active,
        .hero-btn:active {
            animation: iconPulse 0.2s ease;
        }

        @keyframes iconPulse {
            0% { transform: scale(1.1); }
            50% { transform: scale(1.15); }
            100% { transform: scale(1.1); }
        }

        /* تأثيرات خاصة لأيقونات مهمة */
        #fileUploadBtn:hover::before,
        #micBtn:hover::before,
        #heroMic:hover::before,
        #heroPlus:hover::before {
            animation: fastRotateIcon 2s linear infinite;
        }

        #fileUploadBtn:hover,
        #micBtn:hover,
        #heroMic:hover {
            color: #22e5c7 !important;
        }

        /* تأثير خاص لزر Plus */
        #heroPlus:hover {
            color: #10a37f !important;
            background: radial-gradient(circle, rgba(16, 163, 127, 0.2) 0%, rgba(16, 163, 127, 0.05) 100%) !important;
        }

        /* تأثير لزر البحث على الويب */
        #webSearchBtn:hover {
            color: #1a9f7a !important;
        }

        #webSearchBtn:hover::before {
            animation: rotateIconBorder 1.5s linear infinite;
        }

        /* تأثير نبضة خفيفة للأيقونات في الحالة العادية */
        .action-btn,
        .hero-btn {
            animation: gentleIconBreathe 4s ease-in-out infinite;
        }

        @keyframes gentleIconBreathe {
            0%, 100% {
                opacity: 0.7;
            }
            50% {
                opacity: 1;
            }
        }

        @keyframes fastRotateIcon {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes rotateIconBorder {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .hero-btn:hover {
            color: var(--text);
            background: rgba(255,255,255,0.08);
        }

        .send-btn {
            background: var(--text);
            border: none;
            color: var(--bg-main);
            width: 32px;
            height: 32px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.2s ease;
            opacity: 0.4;
        }

        .send-btn.enabled {
            opacity: 1;
            background: var(--primary);
            color: white;
        }

        .send-btn:hover.enabled {
            background: var(--primary-dark);
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }

        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 40px;
            color: var(--text);
            position: relative;
            max-width: 768px;
            width: 100%;
            opacity: 1;
            transform: translateY(0);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 10;
        }

        .welcome-screen.fade-out {
            opacity: 0;
            transform: translateY(-50px) scale(0.95);
            pointer-events: none;
            filter: blur(3px);
            transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Enhanced chat container transition - only for chat mode */
        .chat-container.chat-mode {
            transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .chat-container.chat-mode.visible {
            transform: translateY(0) scale(1);
            opacity: 1;
        }

        /* Ensure welcome mode is always visible */
        .chat-container.welcome-mode {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .welcome-icon {
            font-size: 64px;
            margin-bottom: 24px;
            color: var(--primary);
            position: relative;
            display: inline-block;
            animation: gentleBounce 3s ease-in-out infinite;
        }

        .welcome-icon i {
            position: relative;
            z-index: 2;
        }



        @keyframes gentleBounce {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-8px);
            }
        }



        .welcome-greeting {
            font-size: 28px;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 16px;
            text-align: center;
            opacity: 0;
            animation: fadeInDown 1s ease-out 0.5s forwards;
        }

        @keyframes fadeInDown {
            0% {
                opacity: 0;
                transform: translateY(-20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes welcomePulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        .empty-icon::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: radial-gradient(ellipse, var(--shadow-light) 0%, transparent 70%);
            animation: shadowPulse 3s ease-in-out infinite;
        }

        @keyframes shadowPulse {
            0%, 100% { transform: translateX(-50%) scale(1); opacity: 0.3; }
            50% { transform: translateX(-50%) scale(1.2); opacity: 0.6; }
        }

        /* AI pulse animation for robot icon */
        @keyframes ai-pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
                filter: brightness(1.2);
            }
        }

        /* Enhanced typing bounce animation */
        @keyframes ai-typing-bounce {
            0%, 80%, 100% {
                transform: scale(0.8) translateY(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2) translateY(-8px);
                opacity: 1;
            }
        }

        /* Slide in animation for code blocks */
        @keyframes slideInCode {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Ripple effect for copy button */
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .welcome-title {
            font-size: 40px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text);
            line-height: 1.2;
            letter-spacing: -0.5px;
        }

        .empty-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--text), transparent);
        }

        .welcome-description {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 0;
            line-height: 1.5;
            max-width: 480px;
            font-weight: 400;
        }
        /* Hero ask bar like ChatGPT */
        .hero-input {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.06);
            border: 1px solid var(--border-light);
            border-radius: 999px;
            padding: 10px 12px;
            width: 640px;
            max-width: calc(100vw - 64px);
            box-shadow: 0 6px 30px rgba(0,0,0,0.25);
            backdrop-filter: blur(8px);
            margin-top: 28px;
            transition: all 0.3s ease;
        }

        .hero-input:hover,
        .hero-input:focus-within {
            border-color: var(--primary);
            box-shadow: 0 8px 35px rgba(0,0,0,0.3);
        }
        .hero-field{
            flex: 1;
            background: transparent;
            border: none;
            outline: none;
            color: var(--text);
            font-size: 14px;
            resize: none;
            max-height: 140px;
        }
        .hero-btn{
            width: 32px; height: 32px;
            border-radius: 50%;
            background: transparent;
            color: var(--text-secondary);
            border: none; cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        /* ألوان خضراء متحركة حول أزرار البطاقة الرئيسية */
        .hero-btn::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: conic-gradient(
                from 0deg,
                transparent,
                #10a37f,
                transparent,
                #22e5c7,
                transparent,
                #1a9f7a,
                transparent
            );
            border-radius: 50%;
            z-index: -1;
            animation: rotateHeroBorder 3s linear infinite;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .hero-btn:hover::before {
            opacity: 1;
        }

        .hero-btn:hover{ 
            color: var(--primary); 
            background: rgba(16, 163, 127, 0.1);
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(16, 163, 127, 0.3);
        }

        @keyframes rotateHeroBorder {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        .hero-actions{ display:flex; align-items:center; gap:6px; }
        .hero-send{
            width: 32px; height: 32px; border-radius: 50%;
            border: none; cursor: pointer;
            background: var(--text); color: var(--bg-main);
            opacity: .4; transition: .2s;
        }

        .hero-send.enabled{ 
            opacity:1;
            background: var(--primary);
            color: white;
        }

        .hero-send.enabled:hover {
            transform: scale(1.05);
            background: var(--primary-dark);
        }

        @media (max-width: 600px){
            .hero-input{ 
                width: 100%; 
                border-radius: 20px; 
            }
            
            /* تقليل سرعة الأنيميشن على الأجهزة المحمولة لتحسين الأداء */
            .action-btn::before,
            .hero-btn::before {
                animation-duration: 4s;
            }
        }

        /* إيقاف الأنيميشن للأجهزة التي تفضل أداء أفضل */
        @media (prefers-reduced-motion: reduce) {
            .action-btn::before,
            .hero-btn::before {
                animation: none;
            }
            
            .action-btn:active,
            .hero-btn:active {
                animation: none;
            }
        }

        /* Hide bottom input while in welcome */
        .chat-container.welcome-mode ~ .input-container{ display: none; }

        .suggestions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 16px;
            max-width: 800px;
            width: 100%;
        }

        .suggestion {
            background: var(--secondary);
            border: 2px solid var(--border);
            border-radius: 12px;
            padding: 20px;
            text-align: right;
            cursor: pointer;
            transition: all var(--transition-medium);
            font-size: 15px;
            color: var(--text);
            position: relative;
            overflow: hidden;
        }

        .suggestion::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,0,0,0.03), transparent);
            transition: left 0.5s ease;
        }

        .suggestion:hover {
            background: var(--bg-light);
            border-color: var(--text);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px var(--shadow-medium);
        }

        .suggestion:hover::before {
            left: 100%;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.7;
            color: var(--primary);
        }

        .empty-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .empty-description {
            font-size: 16px;
            max-width: 500px;
            line-height: 1.6;
        }

        /* File Upload Modal */
        .file-upload-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 24px;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .close-btn:hover {
            color: var(--danger);
        }

        .upload-options {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .upload-option {
            border: 2px solid var(--border);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-option:hover {
            border-color: var(--primary);
            background-color: rgba(37, 99, 235, 0.05);
        }

        .upload-icon {
            font-size: 36px;
            color: var(--primary);
            margin-bottom: 12px;
        }

        .upload-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .upload-description {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .image-upload-area {
            border: 2px dashed var(--border);
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
            display: none;
        }

        .image-upload-area:hover {
            border-color: var(--primary);
            background-color: rgba(37, 99, 235, 0.05);
        }

        .upload-area-icon {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .upload-area-text {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .upload-area-subtext {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .file-input {
            display: none;
        }

        .google-sheets-form {
            display: none;
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border);
            border-radius: 6px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .modal-btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cancel-btn {
            background-color: var(--bg-light);
            color: var(--text);
        }

        .cancel-btn:hover {
            background-color: var(--border);
        }

        /* Pro Upload button visual feedback */
        .action-btn#fileUploadBtn { position: relative; overflow: hidden; }
        .action-btn#fileUploadBtn::after{
            content:''; position:absolute; inset:0; background: radial-gradient(120px 60px at var(--x,50%) var(--y,50%), rgba(255,255,255,0.12), transparent 60%);
            opacity:0; transition: opacity .25s ease;
        }
        .action-btn#fileUploadBtn:hover::after{ opacity:1; }
        .action-btn#fileUploadBtn.uploading { color: var(--primary); }

        /* Drag-and-drop highlight */
        .image-upload-area.dragover, .document-upload-area.dragover{
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37,99,235,0.15);
            background-color: rgba(37, 99, 235, 0.05);
        }

        .upload-confirm-btn {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
        }

        .upload-confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        /* Confirmation Dialog */
        .confirm-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .confirm-dialog.show {
            display: flex;
        }

        .confirm-content {
            background-color: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .confirm-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text);
        }

        .confirm-message {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .confirm-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .confirm-btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .confirm-cancel {
            background-color: var(--bg-light);
            color: var(--text);
        }

        .confirm-cancel:hover {
            background-color: var(--border);
        }

        .confirm-ok {
            background-color: var(--danger);
            color: white;
        }

        .confirm-ok:hover {
            background-color: #dc2626;
        }

        /* API Status - Hidden */
        .api-status {
            display: none;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-light);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* Advanced Responsive Design */

        /* Large Tablets (1024px - 1200px) */
        @media (max-width: 1200px) and (min-width: 1024px) {
            .input-wrapper-container {
                max-width: 90%;
            }

            .messages-area {
                max-width: 90%;
            }

            .welcome-screen {
                max-width: 90%;
                padding: 40px 20px;
            }

            .welcome-title {
                font-size: 40px;
            }

            .welcome-description {
                font-size: 18px;
            }
        }

        /* Standard Tablets (768px - 1024px) */
        @media (max-width: 1024px) and (min-width: 768px) {
            :root {
                --sidebar-width: 72px;
            --sidebar-expanded-width: 280px;
            }

            .floating-chat-icon {
                width: 100px;
                height: 100px;
                font-size: 40px;
            }

            .empty-title {
                font-size: 36px;
            }

            .empty-description {
                font-size: 16px;
            }

            .suggestions {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 14px;
            }

            .message {
                padding: 20px;
            }

            .avatar {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .input-container {
                padding: 20px;
            }

            .input-form {
                padding: 14px 18px;
            }
        }

        /* Mobile Landscape & Small Tablets (480px - 768px) */
        @media (max-width: 768px) and (min-width: 480px) {
            :root {
                --sidebar-width: 72px;
            --sidebar-expanded-width: 280px;
            }

            .sidebar {
                box-shadow: -8px 0 30px var(--shadow-medium);
            }

            .floating-chat-icon {
                width: 80px;
                height: 80px;
                font-size: 32px;
            }

            .empty-title {
                font-size: 28px;
            }

            .empty-description {
                font-size: 15px;
                margin-bottom: 32px;
            }

            .suggestions {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .suggestion {
                padding: 16px;
                font-size: 14px;
            }

            .input-container {
                padding: 16px;
            }

            .input-wrapper-container {
                max-width: 100%;
            }

            .message {
                padding: 16px;
            }

            .avatar {
                width: 32px;
                height: 32px;
                font-size: 12px;
                margin-left: 12px;
            }

            .message-content {
                font-size: 14px;
                padding-right: 12px;
            }

            .messages-area {
                padding: 16px 0 140px;
            }

            .header {
                padding: 12px 16px;
            }

            .header-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        /* Small Mobile (320px - 480px) */
        @media (max-width: 480px) {
            .floating-chat-icon {
                width: 70px;
                height: 70px;
                font-size: 28px;
            }

            .welcome-screen {
                padding: 40px 16px;
            }

            .welcome-greeting {
                font-size: 24px;
            }

            .welcome-icon {
                font-size: 70px;
            }

            .welcome-title {
                font-size: 32px;
            }

            .welcome-description {
                font-size: 16px;
            }

            .empty-title {
                font-size: 24px;
                margin-bottom: 16px;
            }

            .empty-description {
                font-size: 14px;
                margin-bottom: 28px;
            }

            .suggestions {
                gap: 10px;
            }

            .suggestion {
                padding: 14px;
                font-size: 13px;
            }

            .input-container {
                padding: 12px;
            }

            .input-form {
                padding: 12px 16px;
                border-radius: 12px;
            }

            .input-field {
                font-size: 15px;
            }

            .action-btn {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .send-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .message {
                padding: 12px;
            }

            .avatar {
                width: 28px;
                height: 28px;
                font-size: 11px;
                margin-left: 8px;
            }

            .message-content {
                font-size: 13px;
                padding-right: 8px;
            }

            .messages-area {
                padding: 12px 0 130px;
            }

            .header {
                padding: 10px 12px;
            }

            .header-btn {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .logo-text {
                font-size: 16px;
            }
        }

        /* Extra Small Mobile (< 320px) */
        @media (max-width: 320px) {
            .floating-chat-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }

            .empty-title {
                font-size: 20px;
            }

            .empty-description {
                font-size: 13px;
            }

            .input-container {
                padding: 10px;
            }

            .message {
                padding: 10px;
            }

            .avatar {
                width: 24px;
                height: 24px;
                font-size: 10px;
                margin-left: 6px;
            }
        }

            .header {
                padding: 12px 16px;
            }

            .logo-text {
                font-size: 18px;
            }

            .model-info {
                font-size: 11px;
            }

            .input-container {
                padding: 12px 16px;
                gap: 8px;
            }

            .input-field {
                font-size: 16px;
                padding: 10px 16px;
                min-height: 44px;
            }

            .action-btn, .send-btn {
                width: 44px;
                height: 44px;
                font-size: 16px;
            }

            .chat-container {
                padding: 16px 12px;
                gap: 12px;
            }

            .avatar {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .empty-state {
                padding: 40px 20px;
            }

            .empty-icon {
                font-size: 48px;
            }

            .empty-title {
                font-size: 20px;
            }

            .empty-description {
                font-size: 14px;
            }

            .sidebar-header {
                margin-bottom: 16px;
                font-size: 14px;
            }

            .category-btn {
                padding: 10px;
                font-size: 16px;
            }

            .chat-history {
                padding: 12px;
            }

            .chat-item {
                padding: 10px 12px;
            }

            .chat-item-icon {
                width: 28px;
                height: 28px;
            }

            .modal-content {
                margin: 20px;
                padding: 20px;
                max-height: 85vh;
            }

            .upload-option {
                padding: 16px;
            }

            .upload-icon {
                font-size: 28px;
            }

            .upload-title {
                font-size: 16px;
            }

        @media (max-width: 480px) {
            .message-content {
                max-width: 90%;
                font-size: 14px;
                padding: 10px 14px;
            }

            .header {
                padding: 10px 12px;
            }

            .logo-text {
                font-size: 16px;
            }

            .input-container {
                padding: 10px 12px;
                gap: 6px;
            }

            .input-field {
                font-size: 16px;
                padding: 8px 14px;
                min-height: 40px;
            }

            .action-btn, .send-btn {
                width: 40px;
                height: 40px;
                font-size: 14px;
            }

            .chat-container {
                padding: 12px 8px;
                gap: 10px;
            }

            .avatar {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            .welcome-screen {
                padding: 30px 16px;
            }

            .welcome-greeting {
                font-size: 22px;
            }

            .welcome-icon {
                font-size: 60px;
            }

            .welcome-title {
                font-size: 28px;
            }

            .welcome-description {
                font-size: 15px;
            }

            .empty-icon {
                font-size: 40px;
            }

            .empty-title {
                font-size: 18px;
            }

            .empty-description {
                font-size: 13px;
            }

            .modal-content {
                margin: 10px;
                padding: 16px;
                max-height: 90vh;
            }

            .modal-title {
                font-size: 18px;
            }

            .upload-option {
                padding: 12px;
            }

            .upload-icon {
                font-size: 24px;
            }

            .upload-title {
                font-size: 14px;
            }

            .upload-description {
                font-size: 12px;
            }
        }

        /* Landscape orientation for mobile */
        @media (max-width: 768px) and (orientation: landscape) {
            .chat-container {
                padding: 12px;
            }

            .input-container {
                padding: 8px 12px;
            }

            .header {
                padding: 8px 16px;
            }

            .sidebar-header {
                margin-bottom: 12px;
            }

            .sidebar-nav-item .tooltip,
            .user-profile .tooltip {
                display: none;
            }
            
            .sidebar-nav-item {
                width: 40px;
                height: 40px;
            }
            
            .sidebar.expanded .sidebar-nav-item {
                width: 100%;
                height: 44px;
            }
            
            .user-profile {
                width: 40px;
                height: 40px;
            }
            
            .sidebar.expanded .user-profile {
                width: 100%;
                height: 44px;
            }
            
            .sidebar-brand {
                width: 36px;
                height: 36px;
                font-size: 16px;
            }
        }

        /* Touch improvements */
        @media (hover: none) and (pointer: coarse) {
            .header-btn, .action-btn, .send-btn, .category-btn {
                min-height: 44px;
                min-width: 44px;
            }

            .chat-item {
                min-height: 48px;
            }

            .category-item {
                min-height: 44px;
                padding: 12px 16px;
            }

            .new-chat-btn {
                min-height: 44px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div style="display: flex; align-items: center;">
                    <div class="sidebar-brand">
                        <i class="fas fa-robot"></i>
                    </div>
                    <span class="sidebar-brand-text">EAM</span>
                </div>
                <button class="expand-btn" id="expandSidebarBtn" title="توسيع الشريط الجانبي">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="sidebar-nav">
                <button class="sidebar-nav-item active" id="newChatBtn" title="دردشة جديدة">
                    <i class="fas fa-plus"></i>
                    <span class="sidebar-nav-item-text">دردشة جديدة</span>
                    <span class="tooltip">دردشة جديدة</span>
                </button>
                
                <button class="sidebar-nav-item" id="searchBtn" title="بحث">
                    <i class="fas fa-search"></i>
                    <span class="sidebar-nav-item-text">البحث</span>
                    <span class="tooltip">بحث في الدردشات</span>
                </button>
                
                <button class="sidebar-nav-item" id="historyBtn" title="تاريخ الدردشات">
                    <i class="fas fa-history"></i>
                    <span class="sidebar-nav-item-text">المحادثات</span>
                    <span class="tooltip">تاريخ الدردشات</span>
                </button>
                
                <button class="sidebar-nav-item" id="libraryBtn" title="المكتبة">
                    <i class="fas fa-bookmark"></i>
                    <span class="sidebar-nav-item-text">المكتبة</span>
                    <span class="tooltip">المكتبة</span>
                </button>
                
                <button class="sidebar-nav-item" id="settingsBtn" title="الإعدادات">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-nav-item-text">الإعدادات</span>
                    <span class="tooltip">الإعدادات</span>
                </button>
            </div>

            <!-- Dynamic Content Area -->
            <div class="sidebar-content" id="sidebarContent">
                
                <!-- Search Content -->
                <div class="sidebar-section" id="searchSection" style="display: none;">
                    <div class="section-header">
                        <h3>البحث في المحادثات</h3>
                    </div>
                    <div class="search-container">
                        <input type="text" id="chatSearchInput" placeholder="ابحث في محادثاتك..." class="search-input">
                        <div class="search-results" id="searchResults"></div>
                    </div>
                </div>

                <!-- History Content -->
                <div class="sidebar-section" id="historySection" style="display: none;">
                    <div class="section-header">
                        <h3>تاريخ المحادثات</h3>
                        <button class="clear-history-btn" id="clearHistoryBtn" title="مسح جميع المحادثات">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="chat-list" id="chatHistoryList">
                        <!-- Chat history items will be added here -->
                    </div>
                </div>

                <!-- Library Content -->
                <div class="sidebar-section" id="librarySection" style="display: none;">
                    <div class="section-header">
                        <h3>المكتبة</h3>
                        <button class="add-bookmark-btn" id="addBookmarkBtn" title="حفظ المحادثة الحالية">
                            <i class="fas fa-bookmark-plus"></i>
                        </button>
                    </div>
                    <div class="bookmark-list" id="bookmarkList">
                        <!-- Bookmarked conversations will be added here -->
                    </div>
                </div>

                <!-- Settings Content -->
                <div class="sidebar-section" id="settingsSection" style="display: none;">
                    <div class="section-header">
                        <h3>الإعدادات</h3>
                    </div>
                    <div class="settings-content">
                        <div class="setting-group">
                            <label>المظهر</label>
                            <select id="themeSelect">
                                <option value="auto">تلقائي</option>
                                <option value="dark">داكن</option>
                                <option value="light">فاتح</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label>حجم الخط</label>
                            <select id="fontSizeSelect">
                                <option value="small">صغير</option>
                                <option value="medium">متوسط</option>
                                <option value="large">كبير</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label>إعدادات المحادثة</label>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="autoSaveChats" checked>
                                    <span>حفظ المحادثات تلقائياً</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="showTimestamps" checked>
                                    <span>عرض التوقيت</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="sidebar-footer">
                <div class="user-profile" title="الملف الشخصي">
                    <div class="user-avatar-circle">م</div>
                    <div class="user-info-expanded">
                        <div class="user-name">المستخدم</div>
                        <div class="user-email"><EMAIL></div>
                    </div>
                    <span class="tooltip">المستخدم</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <div class="logo">
                    <div>
                        <div class="logo-text">Elashrafy AI Model</div>
                        <div class="model-info">نظام تجريبي للذكاء الاصطناعي</div>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="beta-icon" title="نظام تجريبي">
                        <i class="fas fa-robot"></i>
                        <span>تجريبي</span>
                    </div>
                    <button class="header-btn" id="toggleSidebar" title="الشريط الجانبي">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="header-btn" id="settingsBtn" title="الإعدادات">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>

            </div>

            <div class="chat-container welcome-mode" id="chatContainer">
                <!-- Welcome Screen -->
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="welcome-greeting">أهلاً وسهلاً بك!</div>
                    <div class="welcome-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="welcome-title">تقديم EAM الجديد</div>
                    <div class="welcome-description">
                        نموذج الذكاء الاصطناعي الأشرافي الأكثر تطوراً، مع قدرات محسّنة في التفكير والإبداع.
                    </div>

                    <!-- Centered Ask box like the reference -->
                    <div class="hero-input" id="heroInput">
                        <button class="hero-btn" id="heroPlus" title="جديد/إرفاق">
                            <i class="fas fa-plus"></i>
                        </button>
                        <textarea class="hero-field" id="heroMessage" placeholder="اسأل أي شيء..." rows="1"></textarea>
                        <div class="hero-actions">
                            <button class="hero-btn" id="heroMic" title="ميكروفون">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <button class="hero-send" id="heroSend" title="إرسال">
                                <i class="fas fa-arrow-up"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Messages Area (hidden initially) -->
                <div class="messages-area" id="messagesArea">
                    <!-- Messages will be added here -->
                </div>
            </div>

            <!-- Input Container -->
            <div class="input-container">
                <div class="input-form">
                    <textarea
                        class="input-field"
                        id="messageInput"
                        placeholder="اسأل أي شيء..."
                        autocomplete="off"
                        rows="1"
                    ></textarea>
                    <div class="input-actions">
                        <button class="action-btn" id="fileUploadBtn" title="رفع ملف">
                            <i class="fas fa-cloud-arrow-up"></i>
                        </button>
                        <button class="action-btn" id="webSearchBtn" title="البحث في الإنترنت">
                            <i class="fas fa-globe"></i>
                        </button>
                        <button class="action-btn" id="micBtn" title="ميكروفون">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button class="send-btn" id="sendBtn">
                            <i class="fas fa-arrow-up"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Status (Hidden) -->
    <div class="api-status">
        <div class="status-indicator"></div>
        <span>النظام التجريبي متصل</span>
    </div>

    <!-- File Upload Modal -->
    <div class="file-upload-modal" id="fileUploadModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">رفع ملف</div>
                <button class="close-btn" id="closeModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="upload-options">
                <div class="upload-option" id="imageUploadOption">
                    <div class="upload-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="upload-title">رفع صورة</div>
                    <div class="upload-description">PNG, JPG, GIF حتى 10MB</div>
                </div>

                <div class="upload-option" id="documentUploadOption">
                    <div class="upload-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="upload-title">رفع مستند</div>
                    <div class="upload-description">PDF, DOC, DOCX حتى 10MB</div>
                </div>

                <div class="upload-option" id="googleSheetsOption">
                    <div class="upload-icon">
                        <i class="fas fa-table"></i>
                    </div>
                    <div class="upload-title">جوجل شيت</div>
                    <div class="upload-description">ربط بيانات من جوجل شيت</div>
                </div>
            </div>

            <div class="image-upload-area" id="imageUploadArea">
                <div class="upload-area-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="upload-area-text">اسحب الصورة هنا أو انقر للاختيار</div>
                <div class="upload-area-subtext">PNG, JPG, GIF حتى 10MB</div>
                <input type="file" id="imageFileInput" class="file-input" accept="image/*">
            </div>

            <div class="image-upload-area" id="documentUploadArea">
                <div class="upload-area-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="upload-area-text">اسحب المستند هنا أو انقر للاختيار</div>
                <div class="upload-area-subtext">PDF, DOC, DOCX حتى 10MB</div>
                <input type="file" id="documentFileInput" class="file-input" accept=".pdf,.doc,.docx">
            </div>

            <div class="google-sheets-form" id="googleSheetsForm">
                <div class="form-group">
                    <label class="form-label">رابط جوجل شيت</label>
                    <input type="url" class="form-input" id="sheetUrl" placeholder="https://docs.google.com/spreadsheets/...">
                </div>
                <div class="form-group">
                    <label class="form-label">اسم الورقة</label>
                    <input type="text" class="form-input" id="sheetName" placeholder="Sheet1">
                </div>
            </div>

            <div class="modal-actions" id="modalActions" style="display: none;">
                <button class="modal-btn cancel-btn" id="cancelUploadBtn">إلغاء</button>
                <button class="modal-btn upload-confirm-btn" id="confirmUploadBtn">إرسال</button>
            </div>
        </div>
    </div>

    <!-- Confirmation Dialog -->
    <div class="confirm-dialog" id="confirmDialog">
        <div class="confirm-content">
            <div class="confirm-title">تأكيد الحذف</div>
            <div class="confirm-message">هل أنت متأكد من حذف هذه المحادثة؟ لا يمكن التراجع عن هذا الإجراء.</div>
            <div class="confirm-actions">
                <button class="confirm-btn confirm-cancel" id="confirmCancel">إلغاء</button>

                <button class="confirm-btn confirm-ok" id="confirmOk">حذف</button>
            </div>
        </div>
    </div>


    <script>
        // Model and API configuration - TESTING WITH WORKING MODEL
        const API_URL = "https://openrouter.ai/api/v1/chat/completions";
        const MODELS = {
            gptoss: {
                id: 'gptoss',
                label: 'EAM - نظام تجريبي',
                model: 'openai/gpt-oss-20b',
                apiKey: '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',
                capabilities: {
                    chat: true,
                    stream: true,
                    images: false,
                    coding: true,
                    syntaxHighlighting: true,
                    lineNumbers: true
                }
            }
        };
        let SELECTED_MODEL_ID = 'gptoss'; // Always use GPT-OSS model
        function getActiveModel(){ return MODELS.gptoss; }

        const ENABLE_STREAMING = true; // Use SSE when available
        const REQUEST_TIMEOUT_MS = 30000;

        // Optimized parameters for faster responses
        function getOptimizedParams() {
            const activeModel = getActiveModel();
            if (activeModel.id === 'qwen') {
                return {
                    temperature: 0.1,  // Lower for more focused responses
                    max_tokens: 800,   // Increased for code examples
                    top_p: 0.9,
                    frequency_penalty: 0.1,
                    presence_penalty: 0.1
                };
            }
            return {
                temperature: 0.2,
                max_tokens: 600,
                top_p: 0.95
            };
        }

        // Lazy-load external libraries when needed
        async function loadExternalLib(src, globalCheck){
            if (globalCheck && globalCheck()) return;
            await new Promise((resolve, reject)=>{
                const s = document.createElement('script'); s.src = src; s.onload = resolve; s.onerror = reject; document.head.appendChild(s);
            });
        }

        // Analyze uploaded files with the LLM (basic client-side implementation)
        async function analyzeUploadedFile(selected, dataUrl){
            const isImage = /\.(png|jpg|jpeg|gif|webp|bmp)$/i.test(selected.name);
            const isPdf = /\.pdf$/i.test(selected.name);
            const isTextLike = /\.(txt|csv|md|json|xml|html)$/i.test(selected.name);

            let prompt = '';
            if (isImage){
                prompt = `حلل الصورة التالية وصفاً دقيقاً واذكر العناصر البارزة، الألوان، النصوص (OCR)، والأفكار. ثم قدم إجابات على أسئلة محتملة.`;
            } else if (isPdf) {
                prompt = `حلل هذا الملف PDF. استخرج الفقرات المهمة وقدم ملخصاً واقتراحات، وحدد الجداول/العناوين إن وجدت.`;
            } else if (isTextLike) {
                prompt = `حلل النص المرفق. لخص المحتوى، استخرج النقاط الرئيسية، قدم اقتراحات تحسين، وبيّن أي أخطاء.`;
            } else {
                prompt = `حلل هذا الملف. حاول التعرف على نوعه ومحتواه ثم لخصه واقترح تحسينات.`;
            }

            // If image, include data URL as context marker; for advanced vision, use a server proxy to a vision model
            const content = isImage ? `${prompt}\n[IMAGE_DATA_URL]\n${dataUrl}` : `${prompt}\n[FILE_NAME] ${selected.name}`;
            const answer = await callAIAPI(content);
            return answer;
        }


        const MAX_HISTORY_MESSAGES = 12; // include only the most recent messages
        const MAX_TOKENS = 600; // smaller for faster responses
        const TEMPERATURE = 0.2;
        // DOM elements
        const sidebar = document.getElementById('sidebar');
        const toggleSidebar = document.getElementById('toggleSidebar');
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const newChatBtn = document.getElementById('newChatBtn');
        const categoryBtn = document.getElementById('categoryBtn');
        const categoryDropdown = document.getElementById('categoryDropdown');
        const welcomeScreen = document.getElementById('welcomeScreen');
        const messagesArea = document.getElementById('messagesArea');
        const webSearchBtn = document.getElementById('webSearchBtn');
        const fileUploadBtn = document.getElementById('fileUploadBtn');
        const fileUploadModal = document.getElementById('fileUploadModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const imageUploadOption = document.getElementById('imageUploadOption');
        const documentUploadOption = document.getElementById('documentUploadOption');
        const googleSheetsOption = document.getElementById('googleSheetsOption');
        const imageUploadArea = document.getElementById('imageUploadArea');
        const documentUploadArea = document.getElementById('documentUploadArea');
        const googleSheetsForm = document.getElementById('googleSheetsForm');
        const imageFileInput = document.getElementById('imageFileInput');
        const documentFileInput = document.getElementById('documentFileInput');
        const modalActions = document.getElementById('modalActions');
        const cancelUploadBtn = document.getElementById('cancelUploadBtn');
        const confirmUploadBtn = document.getElementById('confirmUploadBtn');
        const chatHistoryList = document.getElementById('chatHistoryList');
        const confirmDialog = document.getElementById('confirmDialog');
        const confirmCancel = document.getElementById('confirmCancel');
        const confirmOk = document.getElementById('confirmOk');

        // Chat history and context
        let chatHistory = [];
        let currentChatId = null;
        let isWaitingForResponse = false;
        let selectedFile = null;
        let currentCategory = null;
        let chatToDelete = null;
        let isWebSearchEnabled = false;

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            // Ensure welcome screen is visible on load
            const welcomeScreen = document.getElementById('welcomeScreen');
            const chatContainer = document.getElementById('chatContainer');

            if (welcomeScreen && chatContainer) {
                // Make sure we start in welcome mode
                chatContainer.classList.add('welcome-mode');
                chatContainer.classList.remove('chat-mode', 'visible');
                welcomeScreen.style.display = 'flex';
                welcomeScreen.style.opacity = '1';
                welcomeScreen.style.transform = 'translateY(0)';
                welcomeScreen.classList.remove('fade-out');

                console.log('✅ تم تهيئة شاشة الترحيب بنجاح');
                console.log('📊 حالة العناصر:');
                console.log('- welcomeScreen display:', welcomeScreen.style.display);
                console.log('- chatContainer classes:', chatContainer.className);
                console.log('- welcomeScreen classes:', welcomeScreen.className);
            } else {
                console.error('❌ لم يتم العثور على عناصر شاشة الترحيب');
                console.log('- welcomeScreen:', welcomeScreen);
                console.log('- chatContainer:', chatContainer);
            }

            // Enhanced sidebar initialization
            if (sidebar) {
                console.log('🔧 تهيئة الشريط الجانبي...');

                // الشريط الجانبي مرئي افتراضياً على الكمبيوتر
                if (window.innerWidth > 768) {
                    sidebar.classList.add('open');
                    sidebar.style.transform = 'translateX(0)';
                    console.log('🖥️ تم إظهار الشريط الجانبي للكمبيوتر');

                    // Restore sidebar expanded state
                    const isExpanded = localStorage.getItem('sidebarExpanded') === 'true';
                    if (isExpanded) {
                        sidebar.classList.add('expanded');
                        const expandBtn = document.getElementById('expandSidebarBtn');
                        const icon = expandBtn?.querySelector('i');
                        if (icon) {
                            icon.className = 'fas fa-chevron-left';
                            expandBtn.title = 'تصغير الشريط الجانبي';
                        }
                        console.log('📏 تم توسيع الشريط الجانبي');
                    }
                } else {
                    // Mobile: hide sidebar by default
                    sidebar.classList.remove('open');
                    sidebar.style.transform = 'translateX(-100%)';
                    console.log('📱 تم إخفاء الشريط الجانبي للهاتف');
                }

                console.log('✅ تم تهيئة الشريط الجانبي بنجاح');
                console.log('📊 حالة الشريط الجانبي:');
                console.log('- مفتوح:', sidebar.classList.contains('open'));
                console.log('- موسع:', sidebar.classList.contains('expanded'));
                console.log('- Transform:', sidebar.style.transform);
            } else {
                console.error('❌ لم يتم العثور على الشريط الجانبي');
            }
            messageInput.focus();
            // loadChatHistory(); // تم تعطيلها مؤقتاً
            updateSendButtonState();

            // Auto-resize textarea
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 200) + 'px';
                updateSendButtonState();
            });

            // Web search toggle
            if (webSearchBtn) {
                webSearchBtn.addEventListener('click', () => {
                    isWebSearchEnabled = !isWebSearchEnabled;
                    webSearchBtn.style.backgroundColor = isWebSearchEnabled ? 'var(--primary)' : 'var(--bg-light)';
                    webSearchBtn.style.color = isWebSearchEnabled ? 'white' : 'var(--text)';
                    webSearchBtn.title = isWebSearchEnabled ? 'إلغاء البحث في الإنترنت' : 'تفعيل البحث في الإنترنت';
                });
            }

            // Handle mobile sidebar
            handleMobileSidebar();

            // Handle orientation change
            window.addEventListener('orientationchange', () => {
                setTimeout(() => {
                    handleMobileSidebar();
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }, 100);
            });

            // Handle window resize
            window.addEventListener('resize', () => {
                handleMobileSidebar();
            });
        });

        // Mobile sidebar handling
        function handleMobileSidebar() {
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // Close sidebar when clicking outside
                document.addEventListener('click', (e) => {
                    if (sidebar.classList.contains('open') &&
                        !sidebar.contains(e.target) &&
                        !toggleSidebar.contains(e.target)) {
                        sidebar.classList.remove('open');
                    }
                });

                // Close sidebar when selecting a chat
                const chatItems = document.querySelectorAll('.chat-item');
                chatItems.forEach(item => {
                    item.addEventListener('click', () => {
                        if (window.innerWidth <= 768) {
                            sidebar.classList.remove('open');
                        }
                    });
                });
            }
        }

        // Event listeners
        sendBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Hero input handlers (welcome screen)
        const heroMessage = document.getElementById('heroMessage');
        const heroSend = document.getElementById('heroSend');
        function updateHeroSendState(){
            if (!heroSend || !heroMessage) return;
            const has = heroMessage.value.trim().length>0;
            heroSend.classList.toggle('enabled', has);
            heroSend.disabled = !has;
        }
        if (heroMessage){
            heroMessage.addEventListener('input', updateHeroSendState);
            heroMessage.addEventListener('keydown', (e)=>{
                if (e.key==='Enter' && !e.shiftKey){ e.preventDefault(); heroSend?.click(); }
            });
        }

        // Microphone (Web Speech API) for both welcome and bottom input
        const micButtons = [document.getElementById('heroMic'), document.getElementById('micBtn')].filter(Boolean);
        let isRecording = false;
        let recognizer = null;
        micButtons.forEach(btn => {
            btn.addEventListener('click', async ()=>{
                if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)){
                    alert('المتصفح لا يدعم التعرف على الصوت. يُرجى استخدام Chrome أو Edge.');
                    return;
                }
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                if (!recognizer){
                    recognizer = new SpeechRecognition();
                    recognizer.lang = 'ar-SA';
                    recognizer.interimResults = true;
                    recognizer.continuous = false;
                    recognizer.onstart = ()=>{ isRecording = true; btn.classList.add('recording'); };
                    recognizer.onerror = (e)=>{ isRecording=false; btn.classList.remove('recording'); console.warn('Speech error', e.error); };
                    recognizer.onend = ()=>{ isRecording=false; btn.classList.remove('recording'); };
                    recognizer.onresult = (event)=>{
                        const transcript = Array.from(event.results).map(r=>r[0].transcript).join(' ');
                        if (transcript && event.results[event.results.length-1].isFinal){
                            // Route to the active input
                            if (chatContainer.classList.contains('welcome-mode') && heroMessage){
                                heroMessage.value = transcript; heroSend?.click();
                            } else {
                                messageInput.value = transcript; updateSendButtonState(); sendMessage();
                            }
                        }
                    };
                }
                try {
                    recognizer.start();
                } catch (e) {
                    // start() may throw if already started
                    console.warn('Recognizer start error', e);
                }
            });
        });

        if (heroSend && heroMessage){
            console.log('✅ تم العثور على عناصر شاشة الترحيب');

            heroSend.addEventListener('click', async (e)=>{
                e.preventDefault();
                const text = heroMessage.value.trim();
                console.log('🚀 تم النقر على زر الإرسال، النص:', text);

                if (!text) {
                    console.warn('⚠️ لا يوجد نص للإرسال');
                    return;
                }

                console.log('🔄 بدء عملية الإرسال...');

                // التبديل فوراً إلى وضع الدردشة
                console.log('🎬 تبديل إلى وضع الدردشة...');
                chatContainer.classList.remove('welcome-mode');
                chatContainer.classList.add('chat-mode', 'visible');
                welcomeScreen.style.display = 'none';

                // انتظار قصير للتأكد من الانتقال
                await new Promise(resolve => setTimeout(resolve, 100));

                // نقل النص إلى حقل الإدخال الرئيسي
                messageInput.value = text;
                updateSendButtonState();

                // مسح حقل الترحيب
                heroMessage.value = '';
                updateHeroSendState();

                // إرسال الرسالة
                console.log('📤 إرسال الرسالة...');
                sendMessage();
            });

            updateHeroSendState();
            console.log('✅ تم تهيئة زر الإرسال في شاشة الترحيب');
        } else {
            console.error('❌ لم يتم العثور على عناصر شاشة الترحيب');
            console.log('- heroSend:', heroSend);
            console.log('- heroMessage:', heroMessage);
        }
        newChatBtn.addEventListener('click', startNewChat);
        
        // Sidebar navigation handlers
        const searchBtn = document.getElementById('searchBtn');
        const historyBtn = document.getElementById('historyBtn');
        const libraryBtn = document.getElementById('libraryBtn');
        const settingsBtn = document.getElementById('settingsBtn');
        const expandSidebarBtn = document.getElementById('expandSidebarBtn');
        
        // Function to update active sidebar item
        function updateActiveSidebarItem(activeButton) {
            document.querySelectorAll('.sidebar-nav-item').forEach(btn => {
                btn.classList.remove('active');
            });
            activeButton.classList.add('active');
        }

        searchBtn?.addEventListener('click', () => {
            updateActiveSidebarItem(searchBtn);
            console.log('🔍 تم النقر على البحث');
            toggleSearchMode();
        });

        historyBtn?.addEventListener('click', () => {
            updateActiveSidebarItem(historyBtn);
            console.log('📜 تم النقر على تاريخ الدردشات');
            
            const historySection = document.getElementById('historySection');
            const isCurrentlyActive = historySection.style.display === 'block';
            
            hideAllSidebarSections();
            
            if (!isCurrentlyActive) {
                historySection.style.display = 'block';
                chatManager.updateChatList();
                
                // Auto-expand sidebar if not expanded
                if (!sidebar.classList.contains('expanded')) {
                    expandSidebarBtn?.click();
                }
            }
        });

        libraryBtn?.addEventListener('click', () => {
            updateActiveSidebarItem(libraryBtn);
            console.log('📚 تم النقر على المكتبة');
            toggleLibraryMode();
        });

        settingsBtn?.addEventListener('click', () => {
            updateActiveSidebarItem(settingsBtn);
            console.log('⚙️ تم النقر على الإعدادات');
            toggleSettingsMode();
        });
        
        newChatBtn?.addEventListener('click', () => {
            updateActiveSidebarItem(newChatBtn);
            hideAllSidebarSections(); // Hide sidebar content sections
            chatManager.createNewChat();
        });
        
        // Expand/Collapse sidebar
        expandSidebarBtn?.addEventListener('click', () => {
            sidebar.classList.toggle('expanded');
            const icon = expandSidebarBtn.querySelector('i');
            const isExpanded = sidebar.classList.contains('expanded');
            
            if (isExpanded) {
                icon.className = 'fas fa-chevron-left';
                expandSidebarBtn.title = 'تصغير الشريط الجانبي';
                localStorage.setItem('sidebarExpanded', 'true');
                console.log('🔧 تم توسيع الشريط الجانبي');
            } else {
                icon.className = 'fas fa-chevron-right';
                expandSidebarBtn.title = 'توسيع الشريط الجانبي';
                localStorage.setItem('sidebarExpanded', 'false');
                console.log('🔧 تم تصغير الشريط الجانبي');
            }
        });
        
        // Enhanced toggle sidebar functionality
        toggleSidebar.addEventListener('click', () => {
            sidebar.classList.toggle('open');
            const isOpen = sidebar.classList.contains('open');

            // تحديث transform بناءً على الحالة
            if (isOpen) {
                sidebar.style.transform = 'translateX(0)';
            } else {
                sidebar.style.transform = 'translateX(-100%)';
            }

            console.log(isOpen ? '📱 تم فتح الشريط الجانبي' : '📱 تم إغلاق الشريط الجانبي');

            // Save sidebar state
            localStorage.setItem('sidebarOpen', isOpen.toString());

            // Add visual feedback
            toggleSidebar.style.transform = 'scale(0.95)';
            setTimeout(() => {
                toggleSidebar.style.transform = 'scale(1)';
            }, 150);
        });

        // Enhanced Chat Management System
        const chatManager = {
            chats: JSON.parse(localStorage.getItem('chatHistory') || '[]'),
            bookmarks: JSON.parse(localStorage.getItem('bookmarks') || '[]'),
            currentChatId: null,

            // Create new chat
            createNewChat() {
                const newChat = {
                    id: 'chat_' + Date.now(),
                    title: 'محادثة جديدة',
                    messages: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                this.currentChatId = newChat.id;
                this.saveChat(newChat);
                
                // Clear current messages
                const messagesContainer = document.getElementById('messages');
                if (messagesContainer) {
                    messagesContainer.innerHTML = '';
                }
                
                // Clear input
                const messageInput = document.getElementById('messageInput');
                if (messageInput) messageInput.value = '';
                
                // Return to welcome mode
                switchToWelcomeMode();
                
                console.log('🆕 تم إنشاء محادثة جديدة:', newChat.id);
                return newChat;
            },

            // Save chat to history
            saveChat(chat) {
                const existingIndex = this.chats.findIndex(c => c.id === chat.id);
                if (existingIndex >= 0) {
                    this.chats[existingIndex] = chat;
                } else {
                    this.chats.unshift(chat);
                }
                
                // Keep only last 50 chats
                if (this.chats.length > 50) {
                    this.chats = this.chats.slice(0, 50);
                }
                
                localStorage.setItem('chatHistory', JSON.stringify(this.chats));
                this.updateChatList();
            },

            // Add message to current chat
            addMessage(content, type) {
                if (!this.currentChatId) {
                    this.createNewChat();
                }

                const chat = this.chats.find(c => c.id === this.currentChatId);
                if (chat) {
                    chat.messages.push({
                        content,
                        type,
                        timestamp: new Date().toISOString()
                    });
                    
                    // Update chat title from first user message
                    if (type === 'user' && chat.messages.filter(m => m.type === 'user').length === 1) {
                        chat.title = content.substring(0, 50) + (content.length > 50 ? '...' : '');
                        console.log('📝 تم تحديث عنوان المحادثة:', chat.title);
                    }
                    
                    chat.updatedAt = new Date().toISOString();
                    this.saveChat(chat);
                }
            },

            // Search chats
            searchChats(query) {
                if (!query.trim()) return [];
                
                const results = [];
                const searchTerm = query.toLowerCase();
                
                this.chats.forEach(chat => {
                    // Search in title
                    if (chat.title.toLowerCase().includes(searchTerm)) {
                        results.push({
                            chat,
                            matchType: 'title',
                            preview: chat.title
                        });
                        return;
                    }
                    
                    // Search in messages
                    chat.messages.forEach(message => {
                        if (message.content.toLowerCase().includes(searchTerm)) {
                            results.push({
                                chat,
                                matchType: 'message',
                                preview: message.content.substring(0, 100) + '...'
                            });
                            return;
                        }
                    });
                });
                
                return results.slice(0, 20);
            },

            // Update chat list in sidebar
            updateChatList() {
                const chatList = document.getElementById('chatHistoryList');
                if (!chatList) return;

                chatList.innerHTML = '';

                this.chats.forEach(chat => {
                    const chatItem = document.createElement('div');
                    chatItem.className = `chat-item ${chat.id === this.currentChatId ? 'active' : ''}`;
                    chatItem.innerHTML = `
                        <div class="chat-item-icon">
                            <i class="fas fa-comment"></i>
                        </div>
                        <div class="chat-item-content">
                            <div class="chat-item-title">${chat.title}</div>
                            <div class="chat-item-preview">${new Date(chat.updatedAt).toLocaleDateString('ar-SA')}</div>
                        </div>
                        <div class="chat-item-actions">
                            <button class="chat-action-btn" onclick="chatManager.deleteChat('${chat.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                    
                    chatItem.addEventListener('click', (e) => {
                        if (!e.target.closest('.chat-action-btn')) {
                            this.loadChat(chat.id);
                        }
                    });
                    
                    chatList.appendChild(chatItem);
                });

                if (this.chats.length === 0) {
                    chatList.innerHTML = `
                        <div style="text-align: center; color: var(--text-secondary); padding: 20px; font-style: italic;">
                            💬 لا توجد محادثات محفوظة
                            <br><br>
                            <small>ابدأ محادثة جديدة وستظهر هنا تلقائياً</small>
                        </div>
                    `;
                }
            },

            // Load chat
            loadChat(chatId) {
                const chat = this.chats.find(c => c.id === chatId);
                if (!chat) return null;

                this.currentChatId = chatId;
                
                // Clear current messages
                const messagesContainer = document.getElementById('messages');
                if (messagesContainer) {
                    messagesContainer.innerHTML = '';
                }
                
                // Clear messagesArea as well
                if (window.messagesArea) {
                    messagesArea.innerHTML = '';
                }

                // Load messages using addMessage function
                chat.messages.forEach(message => {
                    addMessage(message.content, message.type === 'user' ? 'user' : 'ai');
                });

                // Switch to chat mode if there are messages
                if (chat.messages.length > 0) {
                    switchToChatMode();
                }

                this.updateChatList();
                console.log('📂 تم تحميل المحادثة:', chatId);
                return chat;
            },

            // Delete chat
            deleteChat(chatId) {
                this.chats = this.chats.filter(c => c.id !== chatId);
                localStorage.setItem('chatHistory', JSON.stringify(this.chats));
                
                if (this.currentChatId === chatId) {
                    this.createNewChat();
                }
                
                this.updateChatList();
                console.log('🗑️ تم حذف المحادثة:', chatId);
            },

            // Bookmark management
            addBookmark(chatId) {
                const chat = this.chats.find(c => c.id === chatId);
                if (!chat) {
                    console.warn('⚠️ لم يتم العثور على المحادثة للحفظ:', chatId);
                    return;
                }

                // Check if already bookmarked
                const existingBookmark = this.bookmarks.find(b => b.chatId === chatId);
                if (existingBookmark) {
                    console.log('📌 المحادثة محفوظة مسبقاً في المكتبة');
                    return;
                }

                const bookmark = {
                    id: 'bookmark_' + Date.now(),
                    chatId: chat.id,
                    title: chat.title,
                    preview: chat.messages.slice(0, 2).map(m => m.content.substring(0, 100)).join(' '),
                    createdAt: new Date().toISOString()
                };

                this.bookmarks.unshift(bookmark);
                localStorage.setItem('bookmarks', JSON.stringify(this.bookmarks));
                this.updateBookmarkList();
                
                console.log('📌 تم حفظ المحادثة في المكتبة');
            },

            deleteBookmark(bookmarkId) {
                this.bookmarks = this.bookmarks.filter(b => b.id !== bookmarkId);
                localStorage.setItem('bookmarks', JSON.stringify(this.bookmarks));
                this.updateBookmarkList();
                console.log('🗑️ تم حذف الإشارة المرجعية');
            },

            updateBookmarkList() {
                const bookmarkList = document.getElementById('bookmarkList');
                if (!bookmarkList) return;

                bookmarkList.innerHTML = '';

                this.bookmarks.forEach(bookmark => {
                    const bookmarkItem = document.createElement('div');
                    bookmarkItem.className = 'bookmark-item';
                    bookmarkItem.innerHTML = `
                        <div style="flex: 1;">
                            <div style="font-size: 14px; font-weight: 500; margin-bottom: 4px; color: var(--text);">${bookmark.title}</div>
                            <div style="font-size: 12px; color: var(--text-secondary); line-height: 1.4;">${bookmark.preview}</div>
                            <div style="font-size: 11px; color: var(--text-secondary); margin-top: 6px;">📅 ${new Date(bookmark.createdAt).toLocaleDateString('ar-SA')}</div>
                        </div>
                        <button onclick="chatManager.deleteBookmark('${bookmark.id}')" style="background: none; border: none; color: var(--text-secondary); cursor: pointer; font-size: 16px; padding: 4px;" title="حذف">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    
                    bookmarkItem.addEventListener('click', (e) => {
                        if (!e.target.closest('button')) {
                            this.loadChat(bookmark.chatId);
                            hideAllSidebarSections();
                        }
                    });
                    
                    bookmarkList.appendChild(bookmarkItem);
                });

                if (this.bookmarks.length === 0) {
                    bookmarkList.innerHTML = `
                        <div style="text-align: center; color: var(--text-secondary); padding: 20px; font-style: italic;">
                            📚 لا توجد محادثات محفوظة في المكتبة
                            <br><br>
                            <small>انقر على زر <strong>+</strong> لحفظ المحادثة الحالية</small>
                            <br>
                            <small style="margin-top: 8px; display: block;">أو احفظ أي محادثة مهمة للرجوع إليها لاحقاً</small>
                        </div>
                    `;
                }
            }
        };

        // Sidebar mode functions
        function toggleSearchMode() {
            const searchSection = document.getElementById('searchSection');
            const isCurrentlyActive = searchSection.style.display === 'block';
            
            hideAllSidebarSections();
            
            if (!isCurrentlyActive) {
                searchSection.style.display = 'block';
                
                // Auto-expand sidebar if not expanded
                if (!sidebar.classList.contains('expanded')) {
                    expandSidebarBtn?.click();
                }
                
                // Focus search input
                setTimeout(() => {
                    document.getElementById('chatSearchInput')?.focus();
                }, 300);
            }
        }

        function toggleLibraryMode() {
            const librarySection = document.getElementById('librarySection');
            const isCurrentlyActive = librarySection.style.display === 'block';
            
            hideAllSidebarSections();
            
            if (!isCurrentlyActive) {
                librarySection.style.display = 'block';
                chatManager.updateBookmarkList();
                
                // Auto-expand sidebar if not expanded
                if (!sidebar.classList.contains('expanded')) {
                    expandSidebarBtn?.click();
                }
            }
        }

        function toggleSettingsMode() {
            const settingsSection = document.getElementById('settingsSection');
            const isCurrentlyActive = settingsSection.style.display === 'block';
            
            hideAllSidebarSections();
            
            if (!isCurrentlyActive) {
                settingsSection.style.display = 'block';
                loadSettings();
                
                // Auto-expand sidebar if not expanded
                if (!sidebar.classList.contains('expanded')) {
                    expandSidebarBtn?.click();
                }
            }
        }

        function hideAllSidebarSections() {
            document.querySelectorAll('.sidebar-section').forEach(section => {
                section.style.display = 'none';
            });
        }

        // Settings functionality
        function loadSettings() {
            const themeSelect = document.getElementById('themeSelect');
            const fontSizeSelect = document.getElementById('fontSizeSelect');
            const autoSaveChats = document.getElementById('autoSaveChats');
            const showTimestamps = document.getElementById('showTimestamps');

            // Load saved settings
            if (themeSelect) themeSelect.value = localStorage.getItem('theme') || 'auto';
            if (fontSizeSelect) fontSizeSelect.value = localStorage.getItem('fontSize') || 'medium';
            if (autoSaveChats) autoSaveChats.checked = localStorage.getItem('autoSaveChats') !== 'false';
            if (showTimestamps) showTimestamps.checked = localStorage.getItem('showTimestamps') !== 'false';

            // Add event listeners
            themeSelect?.addEventListener('change', (e) => {
                localStorage.setItem('theme', e.target.value);
                applyTheme(e.target.value);
                console.log('🎨 تم تغيير المظهر:', e.target.value);
            });

            fontSizeSelect?.addEventListener('change', (e) => {
                localStorage.setItem('fontSize', e.target.value);
                applyFontSize(e.target.value);
                console.log('📏 تم تغيير حجم الخط:', e.target.value);
            });

            autoSaveChats?.addEventListener('change', (e) => {
                localStorage.setItem('autoSaveChats', e.target.checked);
                console.log('💾 حفظ المحادثات التلقائي:', e.target.checked ? 'مُفعل' : 'معطل');
            });

            showTimestamps?.addEventListener('change', (e) => {
                localStorage.setItem('showTimestamps', e.target.checked);
                console.log('⏰ عرض التوقيت:', e.target.checked ? 'مُفعل' : 'معطل');
            });
        }

        function applyTheme(theme) {
            const root = document.documentElement;
            
            switch(theme) {
                case 'dark':
                    root.classList.add('dark-theme');
                    root.classList.remove('light-theme');
                    break;
                case 'light':
                    root.classList.add('light-theme');
                    root.classList.remove('dark-theme');
                    break;
                default: // auto
                    root.classList.remove('dark-theme', 'light-theme');
                    break;
            }
            console.log('🎨 تم تطبيق المظهر:', theme);
        }

        function applyFontSize(size) {
            const root = document.documentElement;
            const sizes = {
                small: '14px',
                medium: '16px',
                large: '18px'
            };
            root.style.setProperty('--base-font-size', sizes[size] || sizes.medium);
            console.log('📏 تم تطبيق حجم الخط:', size);
        }

        // Display Message Function (alias for addMessage)
        function displayMessage(content, type) {
            return addMessage(content, type);
        }

        // Initialize enhanced functionality
        setTimeout(() => {
            // Initialize chat manager
            chatManager.updateChatList();
            chatManager.updateBookmarkList();
            
            // Initialize search functionality
            const searchInput = document.getElementById('chatSearchInput');
            const searchResults = document.getElementById('searchResults');
            
            if (searchInput && searchResults) {
                searchInput.addEventListener('input', (e) => {
                    const query = e.target.value.trim();
                    
                    if (query.length < 2) {
                        searchResults.innerHTML = '';
                        return;
                    }

                    const results = chatManager.searchChats(query);
                    
                    searchResults.innerHTML = '';
                    
                    if (results.length === 0) {
                        searchResults.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 20px;">🔍 لا توجد نتائج<br><small>جرب كلمات مفتاحية مختلفة</small></div>';
                        return;
                    }

                    results.forEach(result => {
                        const resultItem = document.createElement('div');
                        resultItem.className = 'search-result-item';
                        resultItem.innerHTML = `
                            <div class="search-result-title">💬 ${result.chat.title}</div>
                            <div class="search-result-preview">${result.preview}</div>
                        `;
                        
                        resultItem.addEventListener('click', () => {
                            chatManager.loadChat(result.chat.id);
                            searchInput.value = '';
                            searchResults.innerHTML = '';
                            hideAllSidebarSections();
                        });
                        
                        searchResults.appendChild(resultItem);
                    });
                });
            }

            // Clear history button
            const clearHistoryBtn = document.getElementById('clearHistoryBtn');
            clearHistoryBtn?.addEventListener('click', () => {
                if (confirm('⚠️ هل أنت متأكد من حذف جميع المحادثات؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
                    chatManager.chats = [];
                    localStorage.removeItem('chatHistory');
                    chatManager.updateChatList();
                    chatManager.createNewChat();
                    console.log('✅ تم حذف جميع المحادثات');
                    alert('✅ تم حذف جميع المحادثات بنجاح');
                }
            });

            // Add bookmark button functionality
            const addBookmarkBtn = document.getElementById('addBookmarkBtn');
            addBookmarkBtn?.addEventListener('click', () => {
                if (chatManager.currentChatId) {
                    chatManager.addBookmark(chatManager.currentChatId);
                    alert('✅ تم حفظ المحادثة في المكتبة');
                } else {
                    alert('⚠️ لا توجد محادثة حالية للحفظ');
                }
            });

            // Load initial settings
            loadSettings();

            // Close sidebar sections when clicking outside on mobile
            document.addEventListener('click', (e) => {
                if (window.innerWidth <= 768 && !sidebar.contains(e.target)) {
                    hideAllSidebarSections();
                }
            });

            // Auto-collapse sections when sidebar is collapsed
            const expandSidebarBtn = document.getElementById('expandSidebarBtn');
            if (expandSidebarBtn) {
                const originalClick = expandSidebarBtn.onclick;
                expandSidebarBtn.addEventListener('click', () => {
                    // If collapsing sidebar, hide all sections
                    if (sidebar.classList.contains('expanded')) {
                        hideAllSidebarSections();
                    }
                });
            }

            console.log('🎉 تم تهيئة الشريط الجانبي المتقدم بنجاح!');
        }, 1000);

        // Suggestion clicks to fill input like ChatGPT
        document.getElementById('suggestions')?.addEventListener('click', (e) => {
            const target = e.target.closest('.suggestion');
            if (!target) return;
            messageInput.value = target.dataset.text || '';
            messageInput.dispatchEvent(new Event('input'));
            messageInput.focus();
        });

        // Category selection
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                const category = item.dataset.category;
                selectCategory(category);
                categoryDropdown.classList.remove('show');
            });
        });

        // File upload events
        fileUploadBtn.addEventListener('click', () => {
            fileUploadModal.style.display = 'flex';
            resetUploadModal();
        });
        // Hover ripple for upload button
        document.getElementById('fileUploadBtn').addEventListener('mousemove', (e)=>{
            const rect = e.currentTarget.getBoundingClientRect();
            e.currentTarget.style.setProperty('--x', (e.clientX - rect.left)+'px');
            e.currentTarget.style.setProperty('--y', (e.clientY - rect.top)+'px');
        });

        // Drag & Drop handlers
        ['dragenter','dragover'].forEach(evt=>{
            imageUploadArea.addEventListener(evt, (e)=>{ e.preventDefault(); imageUploadArea.classList.add('dragover'); });
            documentUploadArea.addEventListener(evt, (e)=>{ e.preventDefault(); documentUploadArea.classList.add('dragover'); });
        });
        ['dragleave','drop'].forEach(evt=>{
            imageUploadArea.addEventListener(evt, (e)=>{ e.preventDefault(); imageUploadArea.classList.remove('dragover'); });
            documentUploadArea.addEventListener(evt, (e)=>{ e.preventDefault(); documentUploadArea.classList.remove('dragover'); });
        });
        imageUploadArea.addEventListener('drop', (e)=>{
            const f = e.dataTransfer.files?.[0]; if (!f) return; handleFileUpload(f, 'image');
        });
        documentUploadArea.addEventListener('drop', (e)=>{
            const f = e.dataTransfer.files?.[0]; if (!f) return; handleFileUpload(f, 'document');
        });


        // Add missing closeFileModal function
        function closeFileModal() {
            const fileModal = document.getElementById('fileModal');
            if (fileModal) {
                fileModal.style.display = 'none';
            }
        }

        closeModalBtn.addEventListener('click', closeFileModal);
        cancelUploadBtn.addEventListener('click', closeFileModal);

        imageUploadOption.addEventListener('click', () => {
            showImageUpload();
        });

        documentUploadOption.addEventListener('click', () => {
            showDocumentUpload();
        });

        googleSheetsOption.addEventListener('click', () => {
            showGoogleSheetsForm();
        });

        imageUploadArea.addEventListener('click', () => {
            imageFileInput.click();
        });

        documentUploadArea.addEventListener('click', () => {
            documentFileInput.click();
        });

        imageFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileUpload(e.target.files[0], 'image');
            }
        });

        documentFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileUpload(e.target.files[0], 'document');
            }
        });

        // Add missing sendFileMessage function
        function sendFileMessage() {
            console.log('📎 إرسال ملف - هذه الميزة قيد التطوير');
            closeFileModal();
        }

        confirmUploadBtn.addEventListener('click', sendFileMessage);

        // Confirmation dialog events
        confirmCancel.addEventListener('click', () => {
            confirmDialog.classList.remove('show');
            chatToDelete = null;
        });

        confirmOk.addEventListener('click', () => {
            if (chatToDelete) {
                performDeleteChat(chatToDelete);
                confirmDialog.classList.remove('show');
                chatToDelete = null;
            }
        });

        // Functions
        function updateSendButtonState() {
            const hasText = messageInput.value.trim().length > 0;
            if (hasText) {
                sendBtn.classList.add('enabled');
            } else {
                sendBtn.classList.remove('enabled');
            }
        }

        // Functions
        function updateSendButtonState() {
            const hasText = messageInput.value.trim().length > 0;
            sendBtn.disabled = !hasText;
            if (hasText) {
                sendBtn.classList.add('enabled');
            } else {
                sendBtn.classList.remove('enabled');
            }
        }

        function switchToChatMode() {
            if (!chatContainer || !welcomeScreen) {
                console.error('❌ عناصر الانتقال غير موجودة');
                return;
            }

            console.log('🎬 بدء انتقال إلى وضع الدردشة');

            // Enhanced fade-out animation to welcome screen
            welcomeScreen.classList.add('fade-out');

            // Switch container to chat mode with enhanced transition
            chatContainer.classList.remove('welcome-mode');
            chatContainer.classList.add('chat-mode');

            // Add visible class for smooth entrance animation
            setTimeout(() => {
                chatContainer.classList.add('visible');
                console.log('✨ تم إضافة visible class');
            }, 100);

            // Hide welcome screen after enhanced animation
            setTimeout(() => {
                welcomeScreen.style.display = 'none';
                console.log('🎭 تم إخفاء شاشة الترحيب');
            }, 800);

            console.log('✅ تم بدء انتقال وضع الدردشة');
        }

        function switchToWelcomeMode() {
            if (!chatContainer || !welcomeScreen) return;

            console.log('🔄 تبديل إلى وضع الترحيب');

            // Show welcome screen
            welcomeScreen.style.display = 'flex';
            welcomeScreen.classList.remove('fade-out');

            // Switch container mode
            chatContainer.classList.remove('chat-mode', 'visible');
            chatContainer.classList.add('welcome-mode');

            console.log('✅ تم التبديل إلى وضع الترحيب');
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (message === '' || isWaitingForResponse) return;

            console.log('📨 بدء sendMessage مع الرسالة:', message);

            // لا نحتاج للتبديل هنا لأنه تم بالفعل في hero button
            // Switch to chat mode on first message (only if not already switched)
            if (chatContainer.classList.contains('welcome-mode')) {
                console.log('🔄 التبديل إلى وضع الدردشة من sendMessage');
                chatContainer.classList.remove('welcome-mode');
                chatContainer.classList.add('chat-mode', 'visible');
                welcomeScreen.style.display = 'none';
            }

            // Add user message
            addMessage(message, 'user');
            chatHistory.push({ role: 'user', content: message });
            
            // Save message to chat manager
            chatManager.addMessage(message, 'user');

            // Clear input
            messageInput.value = '';
            messageInput.style.height = 'auto';
            updateSendButtonState();

            // Set waiting state
            isWaitingForResponse = true;
            sendBtn.disabled = true;

            try {
                // Prepare message with web search if enabled
                let finalMessage = message;
                if (isWebSearchEnabled) {
                    try {
                        const searchResults = await performWebSearch(message);
                        if (searchResults) {
                            finalMessage = `${message}\n\n[معلومات من الإنترنت: ${searchResults}]`;
                        }
                    } catch (error) {
                        console.error('Web search error:', error);
                    }
                }

                // Enhance message with coding context for better responses
                const activeModel = getActiveModel();
                const codingKeywords = ['كود', 'برمجة', 'function', 'class', 'script', 'debug', 'error', 'algorithm', 'code', 'javascript', 'python', 'html', 'css', 'أكمل', 'استكمال', 'continue', 'complete'];
                const hasCodingContext = codingKeywords.some(keyword =>
                    finalMessage.toLowerCase().includes(keyword.toLowerCase())
                );

                if (hasCodingContext) {
                    // Special handling for continuation requests
                    const isContinuation = ['أكمل', 'استكمال', 'continue', 'complete'].some(keyword =>
                        finalMessage.toLowerCase().includes(keyword.toLowerCase())
                    );

                    if (isContinuation) {
                        // Get the last AI message for context
                        const lastAiMessage = chatHistory.filter(msg => msg.role === 'assistant').pop();
                        const contextMessage = lastAiMessage ? `\n\nالسياق السابق:\n${lastAiMessage.content.substring(0, 500)}...` : '';

                        finalMessage = 'أنت مساعد ذكي متخصص في البرمجة. المطلوب استكمال الكود السابق بشكل متسلسل ومترابط. اكتب الكود كاملاً مع الشرح والتفسير:' + contextMessage + '\n\nالطلب الحالي: ' + finalMessage + '\n\nتعليمات مهمة:\n1. اكتب الكود في كتل منفصلة مع تحديد اللغة البرمجية\n2. اشرح كل جزء من الكود\n3. تأكد من أن الكود متسلسل ومترابط\n4. استخدم تنسيق ```language للكود';
                    } else {
                        finalMessage = 'أنت مساعد ذكي متخصص في البرمجة. قدم حلول برمجية عملية ومفصّلة مع أمثلة الكود:\n\n' + finalMessage + '\n\nتعليمات مهمة:\n1. اكتب الكود في كتل منفصلة مع تحديد اللغة البرمجية\n2. استخدم تنسيق ```language للكود\n3. اشرح كل جزء من الكود\n4. قدم أمثلة عملية';
                    }
                }

                if (ENABLE_STREAMING) {
                    // Create empty AI message bubble and stream tokens into it
                    const messageDiv = addMessage('', 'ai');
                    const aiTextDiv = messageDiv.querySelector('.message-text');
                    let fullText = '';
                    let lastHighlightTime = 0;
                    let retryCount = 0;
                    const maxRetries = 3;

                    // Enhanced loading indicator
                    aiTextDiv.innerHTML = `
                        <div class="loading-indicator" style="display: flex; align-items: center; gap: 12px; padding: 16px; background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(29, 78, 216, 0.1) 100%); border-radius: 12px; border: 1px solid rgba(37, 99, 235, 0.2);">
                            <div class="spinner" style="width: 24px; height: 24px; border: 3px solid rgba(37, 99, 235, 0.3); border-top: 3px solid #2563eb; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                            <div style="color: #2563eb; font-weight: 600; font-size: 14px;">🤖 يعالج طلبك...</div>
                        </div>
                        <style>
                            @keyframes spin {
                                0% { transform: rotate(0deg); }
                                100% { transform: rotate(360deg); }
                            }
                        </style>
                    `;

                    const streamWithRetry = async () => {
                        try {
                            console.log('🚀 بدء إرسال الطلب للذكاء الاصطناعي...');
                            console.log('📝 الرسالة:', finalMessage.substring(0, 100) + '...');

                            await callAIAPIStream(finalMessage, (delta) => {
                                if (!delta) return;
                                fullText += delta;
                                console.log('📥 تم استلام جزء من الرد:', delta);
                                console.log('📝 النص الكامل حتى الآن:', fullText);

                                // عرض النص مباشرة بدون معالجة معقدة
                                aiTextDiv.innerHTML = fullText.replace(/\n/g, '<br>');
                                console.log('🎨 تم تحديث المحتوى في الواجهة');

                                // تمرير تلقائي للأسفل
                                messagesArea.scrollTop = messagesArea.scrollHeight;

                                // Apply enhanced syntax highlighting with optimized timing
                                const now = Date.now();
                                if (now - lastHighlightTime > 300) {  // Optimized frequency for better performance
                                    setTimeout(() => {
                                        if (window.Prism) {
                                            Prism.highlightAllUnder(messageDiv);

                                            // Apply simple syntax highlighting
                                            const codeBlocks = messageDiv.querySelectorAll('.simple-code-block code[class*="language-"]');
                                            codeBlocks.forEach(codeBlock => {
                                                if (window.Prism && Prism.languages[codeBlock.className.replace('language-', '')]) {
                                                    Prism.highlightElement(codeBlock);
                                                }
                                            });
                                        }
                                    }, 50);
                                    lastHighlightTime = now;
                                }

                                messagesArea.scrollTop = messagesArea.scrollHeight;
                            });
                        } catch (streamError) {
                            console.error(`❌ خطأ في الاستجابة المباشرة (المحاولة ${retryCount + 1}):`, streamError);

                            if (retryCount < maxRetries) {
                                retryCount++;
                                aiTextDiv.innerHTML = `
                                    <div class="retry-indicator" style="display: flex; align-items: center; gap: 12px; padding: 16px; background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%); border-radius: 12px; border: 1px solid rgba(255, 193, 7, 0.3);">
                                        <div class="retry-spinner" style="width: 20px; height: 20px; border: 2px solid rgba(255, 193, 7, 0.3); border-top: 2px solid #ffc107; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                                        <div style="color: #ff9800; font-weight: 600; font-size: 13px;">🔄 إعادة المحاولة ${retryCount}/${maxRetries}...</div>
                                    </div>
                                `;
                                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                                return await streamWithRetry();
                            } else {
                                throw streamError;
                            }
                        }
                    };

                    await streamWithRetry();

                    // Final enhanced syntax highlighting
                    if (window.Prism) {
                        setTimeout(() => {
                            Prism.highlightAllUnder(messageDiv);

                            // Final syntax highlighting for simple code blocks
                            const codeBlocks = messageDiv.querySelectorAll('.simple-code-block code[class*="language-"]');
                            codeBlocks.forEach(codeBlock => {
                                if (window.Prism && Prism.languages[codeBlock.className.replace('language-', '')]) {
                                    Prism.highlightElement(codeBlock);
                                }
                            });

                            console.log('✅ تم تطبيق التنسيق النهائي للكود بنجاح');
                        }, 200);
                    }

                    chatHistory.push({ role: 'assistant', content: fullText });
                    
                    // Save AI response to chat manager
                    chatManager.addMessage(fullText, 'ai');
                    
                    const sug = generateSmartSuggestions(message, fullText);
                    if (sug && sug.length) { renderInlineSuggestions(sug); }
                    // updateChatHistory(); // تم تعطيلها مؤقتاً
                } else {
                    // Show enhanced typing indicator for non-streaming
                    showTypingIndicator();
                    messagesArea.scrollTop = messagesArea.scrollHeight;

                    let retryCount = 0;
                    const maxRetries = 3;
                    let aiResponse;

                    const callWithRetry = async () => {
                        try {
                            return await callAIAPI(finalMessage);
                        } catch (apiError) {
                            console.error(`❌ خطأ في استدعاء API (المحاولة ${retryCount + 1}):`, apiError);

                            if (retryCount < maxRetries) {
                                retryCount++;
                                // Update typing indicator to show retry
                                const typingIndicator = document.getElementById('typingIndicator');
                                if (typingIndicator) {
                                    const statusDiv = typingIndicator.querySelector('.typing-status');
                                    if (statusDiv) {
                                        statusDiv.textContent = `🔄 إعادة المحاولة ${retryCount}/${maxRetries}...`;
                                    }
                                }
                                await new Promise(resolve => setTimeout(resolve, 1500 * retryCount));
                                return await callWithRetry();
                            } else {
                                throw apiError;
                            }
                        }
                    };

                    aiResponse = await callWithRetry();

                    // Hide typing indicator
                    hideTypingIndicator();

                    // Add AI response
                    addMessage(aiResponse, 'ai');
                    chatHistory.push({ role: 'assistant', content: aiResponse });
                    
                    // Save AI response to chat manager
                    chatManager.addMessage(aiResponse, 'ai');

                    // Smart suggestions under AI reply
                    const sug = generateSmartSuggestions(message, aiResponse);
                    if (sug && sug.length) { renderInlineSuggestions(sug); }

                    // Update chat history
                    // updateChatHistory(); // تم تعطيلها مؤقتاً
                }
            } catch (error) {
                console.error('❌ خطأ في إرسال الرسالة:', error);

                // Hide typing indicator (in case non-streaming path)
                hideTypingIndicator();

                // Enhanced error message with specific error details and retry options
                const errorMessage = `⚠️ حدث خطأ أثناء الاتصال بالنموذج: ${error.message || 'خطأ غير معروف'}\n\n🔄 يمكنك:\n• إعادة إرسال الرسالة\n• تبسيط الطلب\n• التحقق من الاتصال بالإنترنت\n• المحاولة لاحقاً\n\n💡 نصيحة: إذا استمر الخطأ، جرب إعادة تحميل الصفحة.`;

                addMessage(errorMessage, 'ai');
                chatHistory.push({ role: 'assistant', content: 'حدث خطأ أثناء الاتصال بالنموذج.' });
            } finally {
                // Reset waiting state
                isWaitingForResponse = false;
                sendBtn.disabled = false;
                messageInput.focus();
            }
        }

        // Streaming via OpenRouter SSE
        async function callAIAPIStream(message, onDelta){
            console.log('🌐 إرسال طلب إلى API...');
            console.log('🔑 API Key:', getActiveModel().apiKey.substring(0, 20) + '...');
            console.log('🤖 Model:', getActiveModel().model);

            const controller = new AbortController();
            const timeout = setTimeout(()=>{
                console.error('⏰ انتهت مهلة الطلب');
                controller.abort();
            }, REQUEST_TIMEOUT_MS);

            try {
                const res = await fetch(API_URL, {
                    method:'POST',
                    headers:{
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${getActiveModel().apiKey}`,
                        'HTTP-Referer': window.location.origin,
                        'X-Title': 'Elashrafy AI Model'
                    },
                    body: JSON.stringify({
                        model: getActiveModel().model,
                        messages: buildMessagesPayload(message),
                        stream: true,
                        ...getOptimizedParams()
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeout);
                console.log('📡 استجابة API:', res.status, res.statusText);

                if (!res.ok) {
                    const errorText = await res.text();
                    console.error('❌ خطأ في API:', errorText);
                    throw new Error(`API Error: ${res.status} - ${errorText}`);
                }

                if (!res.body) throw new Error('No response body');

                const reader = res.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let buffer = '';

                while (true){
                    const {value, done} = await reader.read();
                    if (done) break;
                    buffer += decoder.decode(value, {stream:true});
                    const parts = buffer.split('\n');
                    buffer = parts.pop() || '';
                    for (const line of parts){
                        const trimmed = line.trim();
                        if (!trimmed.startsWith('data:')) continue;
                        const dataStr = trimmed.slice(5).trim();
                        if (dataStr === '[DONE]') {
                            console.log('✅ تم الانتهاء من الاستجابة');
                            return;
                        }
                        try {
                            const json = JSON.parse(dataStr);
                            const delta = json.choices?.[0]?.delta?.content || '';
                            if (delta) onDelta(delta);
                        } catch(e) {
                            // ignore malformed chunk
                        }
                    }
                }
            } catch(error) {
                console.error('❌ خطأ في callAIAPIStream:', error);
                throw error;
            }
        }

        function buildMessagesPayload(current){
            const msgs = [];
            if (currentCategory){
                const categorySystemMessages = {
                    'problem-solving': 'أنت مساعد متخصص في حل المسائل بأسلوب منهجي...',
                    'sciences': 'أنت خبير علوم...',
                    'studies': 'أنت مساعد أكاديمي...',
                    'physics': 'أنت فيزيائي...',
                    'philosophy': 'أنت فيلسوف...',
                    'tech-info': 'أنت مهندس برمجيات...',
                    'accounting': 'أنت محاسب قانوني...'
                };
                msgs.push({ role:'system', content: categorySystemMessages[currentCategory] || 'أنت مساعد.' });
            }
            msgs.push(...chatHistory.map(m=>({role:m.role, content:m.content})).slice(-MAX_HISTORY_MESSAGES));
            msgs.push({ role:'user', content: current });
            return msgs;
        }

        async function callAIAPI(message) {
            // Prepare messages for API
            const messages = [];

            // Add system message if category is selected
            if (currentCategory) {
                const categorySystemMessages = {
                    'problem-solving': 'أنت مساعد متخصص في حل المسائل بأسلوب منهجي: افهم المعطيات، حدد المطلوب، ضع خطوات الحل، ثم قدّم الإجابة النهائية مع التحقق وبدائل الحل إن وجدت.',
                    'sciences': 'أنت خبير علوم (أحياء، كيمياء، فيزياء). قدّم إجابات مدعومة بالتفسير العلمي والقوانين والمعادلات والأمثلة العملية والمراجع الموثوقة عند الحاجة.',
                    'studies': 'أنت مساعد أكاديمي. ساعد في منهجية البحث، بناء الفرضيات، طرق التحليل، كتابة الملخصات، والتوثيق بأسلوب APA/MLA عند الطلب.',
                    'physics': 'أنت فيزيائي. حل المسائل بخطوات واضحة: المعطيات، القانون المناسب، التعويض، ثم النتيجة مع الوحدات ومناقشة الدقة.',
                    'philosophy': 'أنت فيلسوف. حلّل الافتراضات والمفاهيم، اطرح حججاً مضادة، وقدّم أمثلة تاريخية ومدارس فكرية ذات صلة.',
                    'tech-info': 'أنت مهندس برمجيات. اشرح التقنيات، قارن الحلول، قدّم أمثلة شفرة بسيطة، ملاحظات أداء وأمان، وأفضل الممارسات.',
                    'accounting': 'أنت محاسب قانوني. طبّق المعايير (IFRS/GAAP) وقدم قيود اليومية، تبويب الحسابات، أمثلة رقمية، وتحذيرات الامتثال.'
                };

                messages.push({
                    role: 'system',
                    content: categorySystemMessages[currentCategory] || 'أنت مساعد ذكاء اصطناعي محترف.'
                });
            }

            // Add chat history
            messages.push(...chatHistory.map(msg => ({
                role: msg.role,
                content: msg.content
            })));

            // Add current message
            messages.push({
                role: 'user',
                content: message
            });

            // Make API request with timeout and adjustable params
            const controller = new AbortController();
            const timeout = setTimeout(() => controller.abort(), REQUEST_TIMEOUT_MS);
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getActiveModel().apiKey}`,
                    'HTTP-Referer': window.location.origin,
                    'X-Title': 'Elashrafy AI Model'
                },
                body: JSON.stringify({
                    model: getActiveModel().model,
                    messages: messages.slice(-12), // Keep recent context
                    stream: false,
                    ...getOptimizedParams()
                }),
                signal: controller.signal
            });
            clearTimeout(timeout);

            // Simple retry once on failure
            if (!response.ok) {
                try {
                    await new Promise(r=>setTimeout(r, 800));
                    const retry = await fetch(API_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${getActiveModel().apiKey}`,
                            'HTTP-Referer': window.location.origin,
                            'X-Title': 'Elashrafy AI Model'
                        },
                        body: JSON.stringify({
                            model: getActiveModel().model,
                            messages: messages.slice(-MAX_HISTORY_MESSAGES),
                            temperature: TEMPERATURE,
                            max_tokens: Math.max(400, Math.floor(MAX_TOKENS*0.75)),
                            stream: false
                        })
                    });
                    if (!retry.ok) {
                        const errorData = await retry.json();
                        throw new Error(errorData.error?.message || 'API request failed');
                    } else {
                        const data = await retry.json();
                        return data.choices?.[0]?.message?.content?.trim() || ' ';
                    }
                } catch (e) {
                    throw e;
                }
            }

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error?.message || 'API request failed');
            }

            const data = await response.json();
            return data.choices[0].message.content.trim();
        }
        // Advanced programming assistance (Qwen3-Coder specialization)
        async function generateCode(prompt, language = 'javascript'){
            const active = getActiveModel();
            if (!active.capabilities.coding){
                return 'الموديل الحالي مخصص للنصوص العامة. بدّل إلى Qwen3-Coder لاستخدام البرمجة والكود.';
            }

            // Enhanced prompt for coding tasks
            const codingPrompt = 'أنت Qwen3-Coder، مطور محترف متخصص في ' + language + '. قم بكتابة كود نظيف ومفصّل مع الشرح:\n\nالطلب: ' + prompt + '\n\nيرجى تقديم:\n1. الكود الكامل مع التعليقات العربية\n2. شرح مفصّل لكل جزء\n3. أمثلة على الاستخدام\n4. نصائح للتحسين والأداء\n5. معالجة الأخطاء المحتملة\n\nتأكد من أن الكود يتبع أفضل الممارسات ومعايير الجودة العالمية.';

            const response = await callAIAPI(codingPrompt);

            // Save generated code to Supabase if available
            if (window.supabaseClient && currentChatId) {
                try {
                    await window.supabaseClient
                        .from('generated_code')
                        .insert([{
                            chat_id: currentChatId,
                            prompt: prompt,
                            language: language,
                            code_content: response,
                            explanation: 'Generated by Qwen3-Coder'
                        }]);
                    console.log('تم حفظ الكود المولّد في Supabase');
                } catch (error) {
                    console.warn('خطأ في حفظ الكود:', error);
                }
            }

            return response;
        }

        // Code review and debugging assistance
        async function reviewCode(code, language = 'javascript'){
            const active = getActiveModel();
            if (!active.capabilities.codeReview){
                return 'الموديل الحالي لا يدعم مراجعة الكود. بدّل إلى Qwen3-Coder.';
            }

            const reviewPrompt = 'أنت خبير في مراجعة الكود. راجع الكود التالي وقدم تحليلاً شاملاً:\n\nاللغة: ' + language + '\nالكود:\n```' + language + '\n' + code + '\n```\n\nيرجى تقديم:\n1. تقييم جودة الكود (1-10)\n2. نقاط القوة\n3. المشاكل والأخطاء المحتملة\n4. اقتراحات للتحسين\n5. أفضل الممارسات المفقودة\n6. مخاوف الأمان إن وجدت';

            return await callAIAPI(reviewPrompt);
        }



            // Generate smart suggestions based on AI response and context
            function generateSmartSuggestions(userMessage, aiResponse){
                const ideas = [];
                const lower = (userMessage||'').toLowerCase();
                if(lower.includes('محاس') || currentCategory==='accounting'){
                    ideas.push('اعطني قيود اليومية مع الشرح');
                    ideas.push('حوّل النتائج إلى قائمة دخل وميزانية');
                    ideas.push('هل هناك مخالفات للمعايير IFRS؟');
                } else if(lower.includes('قانون') || currentCategory==='law'){
                    ideas.push('اذكر المواد القانونية ذات الصلة');
                    ideas.push('صغ مسودة مذكرة قانونية');
                } else if(lower.includes('فيزياء') || currentCategory==='physics'){
                    ideas.push('ما القوانين المستخدمة وخطوات الحل؟');
                    ideas.push('أعطني مثالا رقميا مشابها');
                }
                if(ideas.length===0){
                    ideas.push('لخّص النقاط الرئيسية');
                    ideas.push('اقترح خطوات عملية تالية');
                    ideas.push('هل هناك مراجع موثوقة للتوسع؟');
                }
                return ideas.slice(0,3);
            }

            // Render inline suggestions buttons under the last AI message
            function renderInlineSuggestions(suggestions){
                const lastAiMsg = [...document.querySelectorAll('.message.ai')].pop();
                if(!lastAiMsg) return;
                const wrap = document.createElement('div');
                wrap.className = 'inline-suggestions';
                wrap.style.cssText = 'display:flex;gap:8px;flex-wrap:wrap;margin-top:10px;';
                suggestions.forEach(text=>{
                    const btn = document.createElement('button');
                    btn.className = 'inline-suggestion-btn';
                    btn.textContent = text;
                    btn.style.cssText = 'background:var(--bg-light);border:1px solid var(--border);color:var(--text);padding:8px 12px;border-radius:16px;cursor:pointer;font-size:13px;';
                    btn.addEventListener('click', ()=>{
                        messageInput.value = text;
                        updateSendButtonState();
                        sendMessage();
                    });
                    wrap.appendChild(btn);
                });
                lastAiMsg.querySelector('.message-content').appendChild(wrap);
                messagesArea.scrollTop = messagesArea.scrollHeight;
            }

        async function performWebSearch(query) {
            try {
                // Use Google Custom Search API (you need to replace with your API key)
                const searchQuery = encodeURIComponent(query);
                const response = await fetch(`https://www.googleapis.com/customsearch/v1?key=YOUR_API_KEY&cx=YOUR_SEARCH_ENGINE_ID&q=${searchQuery}&num=3`);

                if (!response.ok) {
                    throw new Error('Search failed');
                }

                const data = await response.json();

                if (data.items && data.items.length > 0) {
                    return data.items.map(item => `${item.title}: ${item.snippet}`).join('\n\n');
                }

                return null;
            } catch (error) {
                console.error('Web search error:', error);
                // Fallback: add web search context to the message
                return `[تم طلب البحث في الإنترنت عن: ${query}]`;
            }
        }

        // Process AI responses with simple code highlighting
        function processCodeResponse(content) {
            if (!content) return '';

            console.log('🔄 معالجة استجابة الذكاء الاصطناعي:', content.substring(0, 100) + '...');

            // Simple regex to detect code blocks
            const codeBlockRegex = /```(\w+)?\s*\n?([\s\S]*?)```/g;
            let processedContent = content;
            let hasCodeBlocks = false;
            let codeBlockCounter = 0;

            // Extract and replace code blocks first
            processedContent = processedContent.replace(codeBlockRegex, (match, language, code) => {
                hasCodeBlocks = true;
                codeBlockCounter++;
                const lang = language ? language.toLowerCase().trim() : 'text';
                const codeId = 'ai-code-' + Date.now() + '-' + codeBlockCounter;
                const cleanCode = code.trim();

                console.log('✅ تم العثور على كتلة كود #' + codeBlockCounter + ':', lang, cleanCode.substring(0, 50) + '...');

                // Enhanced language mapping with more aliases
                const langMap = {
                    'js': 'javascript',
                    'jsx': 'javascript',
                    'ts': 'typescript',
                    'tsx': 'typescript',
                    'py': 'python',
                    'python3': 'python',
                    'html': 'markup',
                    'htm': 'markup',
                    'xml': 'markup',
                    'sh': 'bash',
                    'shell': 'bash',
                    'bash': 'bash',
                    'zsh': 'bash',
                    'css': 'css',
                    'scss': 'scss',
                    'sass': 'sass',
                    'json': 'json',
                    'sql': 'sql',
                    'mysql': 'sql',
                    'postgresql': 'sql',
                    'php': 'php',
                    'java': 'java',
                    'c': 'c',
                    'cpp': 'cpp',
                    'c++': 'cpp',
                    'csharp': 'csharp',
                    'c#': 'csharp',
                    'go': 'go',
                    'rust': 'rust',
                    'swift': 'swift',
                    'kotlin': 'kotlin',
                    'dart': 'dart',
                    'ruby': 'ruby',
                    'perl': 'perl',
                    'r': 'r',
                    'matlab': 'matlab',
                    'yaml': 'yaml',
                    'yml': 'yaml',
                    'toml': 'toml',
                    'ini': 'ini',
                    'dockerfile': 'docker',
                    'docker': 'docker',
                    'makefile': 'makefile',
                    'make': 'makefile'
                };

                const finalLang = langMap[lang] || lang;
                const displayLang = getLanguageDisplayName(lang);

                // Generate simple code block with copy button
                const codeBlockHtml = createSimpleCodeBlock(codeId, finalLang, displayLang, cleanCode);

                console.log('🎨 تم إنشاء كتلة كود محسّنة للغة:', displayLang);
                return codeBlockHtml;
            });

            // Convert inline code
            processedContent = processedContent.replace(/`([^`\n]+)`/g, '<code class="inline-code" style="background: #161b22; color: #79c0ff; padding: 3px 6px; border-radius: 6px; font-family: monospace; font-size: 13px;">$1</code>');

            // Convert line breaks to HTML but preserve code block structure
            processedContent = processedContent.replace(/\n(?![^<]*<\/div>)/g, '<br>');

            console.log('تمت معالجة الاستجابة، تحتوي على كود:', hasCodeBlocks);

            // Apply final enhancements if code blocks were found
            if (hasCodeBlocks) {
                console.log('🚀 تم معالجة ' + codeBlockCounter + ' كتلة كود بنجاح');

                // Schedule syntax highlighting
                setTimeout(() => {
                    if (window.Prism) {
                        const codeElements = document.querySelectorAll('code[id^="qwen-code-"]');
                        codeElements.forEach(codeEl => {
                            if (!codeEl.classList.contains('highlighted')) {
                                Prism.highlightElement(codeEl);
                                codeEl.classList.add('highlighted');
                                updateLineNumbers(codeEl);
                            }
                        });
                        console.log('✨ تم تطبيق syntax highlighting على جميع كتل الكود');
                    }
                }, 100);
            }

            return processedContent;
        }

        // Get display name for programming languages
        function getLanguageDisplayName(lang) {
            const displayNames = {
                'javascript': 'JavaScript',
                'typescript': 'TypeScript',
                'python': 'Python',
                'java': 'Java',
                'cpp': 'C++',
                'csharp': 'C#',
                'php': 'PHP',
                'ruby': 'Ruby',
                'go': 'Go',
                'rust': 'Rust',
                'swift': 'Swift',
                'kotlin': 'Kotlin',
                'dart': 'Dart',
                'html': 'HTML',
                'markup': 'HTML',
                'css': 'CSS',
                'scss': 'SCSS',
                'sass': 'SASS',
                'json': 'JSON',
                'xml': 'XML',
                'yaml': 'YAML',
                'sql': 'SQL',
                'bash': 'Bash',
                'shell': 'Shell',
                'dockerfile': 'Docker',
                'makefile': 'Makefile',
                'text': 'نص عادي'
            };
            return displayNames[lang.toLowerCase()] || lang.toUpperCase();
        }

        // Create simple code block with copy button only
        function createSimpleCodeBlock(codeId, finalLang, displayLang, cleanCode) {
            const languageIcon = getLanguageIcon(finalLang);
            const languageColor = getLanguageColor(finalLang);

            return '<div class="simple-code-block" data-language="' + finalLang + '" style="margin: 16px 0; border-radius: 8px; overflow: hidden; background: #0d1117; border: 1px solid #30363d; position: relative; width: 100%; max-width: 100%;">' +
                '<div class="code-header" style="display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; background: #161b22; border-bottom: 1px solid #30363d; color: #f0f6fc;">' +
                    '<div class="code-language-info" style="display: flex; align-items: center; gap: 8px;">' +
                        '<i class="' + languageIcon + '" style="color: ' + languageColor + '; font-size: 16px;"></i>' +
                        '<span class="code-language" style="font-weight: 600; color: #f0f6fc; font-size: 14px;">' + displayLang + '</span>' +
                    '</div>' +
                    '<button class="copy-code-btn" onclick="copyCode(\'' + codeId + '\')" style="background: #238636; border: none; color: white; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 12px; font-weight: 500; transition: all 0.2s ease; display: flex; align-items: center; gap: 6px;">' +
                        '<i class="fas fa-copy"></i> نسخ' +
                    '</button>' +
                '</div>' +
                '<div class="code-content" style="padding: 0; margin: 0; background: #0d1117; width: 100%; overflow-x: auto;">' +
                    '<pre style="margin: 0; padding: 16px; background: transparent; color: #f0f6fc; font-family: \'Consolas\', \'Monaco\', \'Courier New\', monospace; font-size: 14px; line-height: 1.6; white-space: pre-wrap; word-wrap: break-word; width: 100%; box-sizing: border-box;"><code id="' + codeId + '" class="language-' + finalLang + '" style="background: transparent; color: inherit; display: block; width: 100%; white-space: pre-wrap; word-wrap: break-word;">' + escapeHtml(cleanCode) + '</code></pre>' +
                '</div>' +
            '</div>';
        }

        // Get icon for programming language
        function getLanguageIcon(lang) {
            const icons = {
                'javascript': 'fab fa-js-square',
                'typescript': 'fab fa-js-square',
                'python': 'fab fa-python',
                'java': 'fab fa-java',
                'php': 'fab fa-php',
                'html': 'fab fa-html5',
                'markup': 'fab fa-html5',
                'css': 'fab fa-css3-alt',
                'scss': 'fab fa-sass',
                'sass': 'fab fa-sass',
                'react': 'fab fa-react',
                'vue': 'fab fa-vuejs',
                'angular': 'fab fa-angular',
                'node': 'fab fa-node-js',
                'npm': 'fab fa-npm',
                'git': 'fab fa-git-alt',
                'github': 'fab fa-github',
                'docker': 'fab fa-docker',
                'aws': 'fab fa-aws',
                'linux': 'fab fa-linux',
                'ubuntu': 'fab fa-ubuntu',
                'windows': 'fab fa-windows',
                'apple': 'fab fa-apple'
            };
            return icons[lang.toLowerCase()] || 'fas fa-code';
        }

        // Get color for programming language
        function getLanguageColor(lang) {
            const colors = {
                'javascript': '#f7df1e',
                'typescript': '#3178c6',
                'python': '#3776ab',
                'java': '#ed8b00',
                'cpp': '#00599c',
                'csharp': '#239120',
                'php': '#777bb4',
                'ruby': '#cc342d',
                'go': '#00add8',
                'rust': '#000000',
                'swift': '#fa7343',
                'kotlin': '#7f52ff',
                'dart': '#0175c2',
                'html': '#e34f26',
                'markup': '#e34f26',
                'css': '#1572b6',
                'scss': '#cf649a',
                'sass': '#cf649a',
                'json': '#000000',
                'xml': '#0060ac',
                'yaml': '#cb171e',
                'sql': '#336791',
                'bash': '#4eaa25',
                'shell': '#4eaa25',
                'dockerfile': '#2496ed',
                'makefile': '#427819'
            };
            return colors[lang.toLowerCase()] || '#58a6ff';
        }

        // Update line numbers for code blocks
        function updateLineNumbers(codeElement) {
            const codeId = codeElement.id;
            const lineNumbersDiv = document.getElementById('lines-' + codeId);
            if (!lineNumbersDiv) return;

            const lines = codeElement.textContent.split('\n');
            const lineNumbers = lines.map((_, index) =>
                '<span class="line-number" style="display: block; padding: 0 12px; color: #6e7681; font-weight: 400;">' + (index + 1) + '</span>'
            ).join('');

            lineNumbersDiv.innerHTML = lineNumbers;
            console.log('📊 تم تحديث أرقام الأسطر لكتلة الكود:', codeId);
        }

        // Escape HTML for safe code display
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Copy code function
        function copyCode(codeId) {
            const codeElement = document.getElementById(codeId);
            if (!codeElement) return;

            const text = codeElement.textContent;

            // Use modern clipboard API with fallback
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopySuccess(codeElement);
                }).catch(() => {
                    fallbackCopyTextToClipboard(text, codeElement);
                });
            } else {
                fallbackCopyTextToClipboard(text, codeElement);
            }
        }

        // Preview code function (simplified)
        function previewCode(codeId) {
            alert('ميزة المعاينة متاحة قريباً');
        }

        // Toggle code block collapse/expand
        function toggleCodeBlock(codeId) {
            const codeElement = document.getElementById(codeId);
            if (!codeElement) return;

            const codeContent = codeElement.closest('.code-content');
            const toggleBtn = codeElement.closest('.code-block').querySelector('.code-toggle-btn');
            const codeBlock = codeElement.closest('.code-block');

            if (codeContent.classList.contains('collapsed')) {
                // Expand with smooth animation
                codeContent.classList.remove('collapsed');
                toggleBtn.innerHTML = '<i class="fas fa-compress"></i> طي';
                codeContent.style.maxHeight = '600px';
                codeContent.style.transition = 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                codeBlock.style.transform = 'scale(1)';

                console.log('📖 تم توسيع كتلة الكود');
            } else {
                // Collapse with smooth animation
                codeContent.classList.add('collapsed');
                toggleBtn.innerHTML = '<i class="fas fa-expand"></i> توسيع';
                codeContent.style.maxHeight = '120px';
                codeContent.style.transition = 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                codeBlock.style.transform = 'scale(0.98)';

                // Add visual feedback
                setTimeout(() => {
                    codeBlock.style.transform = 'scale(1)';
                }, 200);

                console.log('📕 تم طي كتلة الكود');
            }

            // Add hover effect to toggle button
            toggleBtn.style.background = 'rgba(33, 38, 45, 0.9)';
            toggleBtn.style.borderColor = '#58a6ff';
            setTimeout(() => {
                toggleBtn.style.background = 'rgba(33, 38, 45, 0.8)';
                toggleBtn.style.borderColor = '#30363d';
            }, 300);
        }



        // Fallback copy function
        function fallbackCopyTextToClipboard(text, codeElement) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopySuccess(codeElement);
            } catch (err) {
                console.error('فشل في نسخ النص:', err);
            }

            document.body.removeChild(textArea);
        }

        // Enhanced copy success feedback with professional animations
        function showCopySuccess(codeElement) {
            const btn = codeElement.closest('.code-block').querySelector('.copy-code-btn');
            if (btn) {
                const originalText = btn.innerHTML;
                const originalStyle = btn.style.background;

                // Enhanced success animation with shine effect
                btn.innerHTML = '<i class="fas fa-check"></i> تم النسخ ✅';
                btn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                btn.style.transform = 'scale(1.08)';
                btn.style.boxShadow = '0 6px 20px rgba(40, 167, 69, 0.4)';
                btn.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

                // Add ripple effect
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(255, 255, 255, 0.6)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple 0.6s linear';
                ripple.style.left = '50%';
                ripple.style.top = '50%';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.marginLeft = '-10px';
                ripple.style.marginTop = '-10px';
                btn.style.position = 'relative';
                btn.appendChild(ripple);

                // Success sound effect (optional)
                try {
                    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmMUCVqo5++sdxMKM5bP8seFLgIVdcXn');
                    audio.volume = 0.3;
                    audio.play().catch(() => {});
                } catch (e) {}

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = originalStyle;
                    btn.style.transform = 'scale(1)';
                    btn.style.boxShadow = '0 4px 12px rgba(35, 134, 54, 0.3)';
                    btn.removeChild(ripple);
                }, 2800);

                console.log('✅ تم نسخ الكود بنجاح مع تأثيرات بصرية محسنة');
            }
        }
        function addMessage(content, sender, fileUrl = null, fileName = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const avatarDiv = document.createElement('div');
            avatarDiv.className = `avatar ${sender}`;
            avatarDiv.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            // Add copy button for AI messages
            if (sender === 'ai') {
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'message-actions';
                actionsDiv.style.cssText = 'position: absolute; top: 8px; left: 8px; opacity: 0; transition: opacity 0.2s;';

                const copyBtn = document.createElement('button');
                copyBtn.className = 'copy-btn';
                copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
                copyBtn.title = 'نسخ النص';
                copyBtn.addEventListener('click', () => copyToClipboard(content, copyBtn));

                actionsDiv.appendChild(copyBtn);
                contentDiv.appendChild(actionsDiv);

                // Show copy button on hover
                messageDiv.addEventListener('mouseenter', () => actionsDiv.style.opacity = '1');
                messageDiv.addEventListener('mouseleave', () => actionsDiv.style.opacity = '0');
            }

            const textDiv = document.createElement('div');
            textDiv.className = 'message-text';

            // Enhanced content processing for Qwen coding responses
            if (sender === 'ai' && getActiveModel().id === 'qwen') {
                // Force code block processing for Qwen responses
                const processedContent = processQwenResponse(content);
                textDiv.innerHTML = processedContent;
                messageDiv.classList.add('qwen-response');

                // Apply enhanced syntax highlighting 
                setTimeout(() => {
                    if (window.Prism) {
                        // Apply syntax highlighting to all code blocks
                        Prism.highlightAllUnder(textDiv);
                        
                        // Apply syntax highlighting to simple code blocks
                        const codeBlocks = textDiv.querySelectorAll('.simple-code-block code[class*="language-"]');
                        codeBlocks.forEach(codeBlock => {
                            // Simple syntax highlighting without line numbers
                            if (window.Prism && Prism.languages[codeBlock.className.replace('language-', '')]) {
                                Prism.highlightElement(codeBlock);
                            }
                        });
                        
                        console.log('تم تطبيق syntax highlighting المحسّن');
                    }
                }, 150);
            } else {
                textDiv.textContent = content;
            }

            contentDiv.appendChild(textDiv);

            // Add file if provided
            if (fileUrl) {
                if (fileName && fileName.match(/\.(jpg|jpeg|png|gif)$/i)) {
                    const img = document.createElement('img');
                    img.src = fileUrl;
                    img.className = 'message-image';
                    img.alt = 'Uploaded image';
                    contentDiv.appendChild(img);
                } else {
                    const fileLink = document.createElement('a');
                    fileLink.href = fileUrl;
                    fileLink.target = '_blank';
                    fileLink.className = 'file-link';
                    fileLink.innerHTML = `<i class="fas fa-file"></i> ${fileName}`;



                    fileLink.style.display = 'inline-block';
                    fileLink.style.marginTop = '8px';
                    fileLink.style.padding = '8px 12px';
                    fileLink.style.backgroundColor = 'var(--bg-light)';
                    fileLink.style.borderRadius = '6px';
                    fileLink.style.textDecoration = 'none';
                    fileLink.style.color = 'var(--text)';
                    contentDiv.appendChild(fileLink);
                }
            }

            messageDiv.appendChild(avatarDiv);
            messageDiv.appendChild(contentDiv);
            messagesArea.appendChild(messageDiv);

            // Apply syntax highlighting if code blocks were added
            if (sender === 'ai' && getActiveModel().id === 'qwen') {
                setTimeout(() => {
                    if (window.Prism) {
                        Prism.highlightAllUnder(messageDiv);
                    }
                }, 150);
            }

            // Scroll to bottom
            messagesArea.scrollTop = messagesArea.scrollHeight;

            return messageDiv;
        }

        function showTypingIndicator() {
            const typingDiv = document.createElement('div');

            typingDiv.className = 'message ai ai-typing';
            typingDiv.id = 'typingIndicator';

            const avatarDiv = document.createElement('div');
            avatarDiv.className = 'avatar ai';

            // Enhanced AI avatar with professional styling
            avatarDiv.innerHTML = '<i class="fas fa-robot" style="color: white;"></i>';
            avatarDiv.style.background = 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)';
            avatarDiv.style.boxShadow = '0 4px 16px rgba(37, 99, 235, 0.4)';
            avatarDiv.style.border = '2px solid rgba(37, 99, 235, 0.3)';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const indicatorDiv = document.createElement('div');
            indicatorDiv.className = 'typing-indicator';

            // Enhanced AI typing indicator with professional styling
            indicatorDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 14px; padding: 16px;">
                    <div style="display: flex; gap: 6px;">
                        <div class="typing-dot" style="background: #10a37f; animation: ai-typing-bounce 1.4s infinite ease-in-out; width: 12px; height: 12px; border-radius: 50%; box-shadow: 0 2px 8px rgba(16, 163, 127, 0.3);"></div>
                        <div class="typing-dot" style="background: #10a37f; animation: ai-typing-bounce 1.4s infinite ease-in-out; animation-delay: 0.2s; width: 12px; height: 12px; border-radius: 50%; box-shadow: 0 2px 8px rgba(16, 163, 127, 0.3);"></div>
                        <div class="typing-dot" style="background: #10a37f; animation: ai-typing-bounce 1.4s infinite ease-in-out; animation-delay: 0.4s; width: 12px; height: 12px; border-radius: 50%; box-shadow: 0 2px 8px rgba(16, 163, 127, 0.3);"></div>
                    </div>
                    <div class="typing-status" style="color: #10a37f; font-weight: 700; font-size: 14px; margin-left: 8px; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                        🤖 الذكاء الاصطناعي يعالج طلبك...
                    </div>
                </div>
            `;
            indicatorDiv.style.background = 'linear-gradient(135deg, rgba(16, 163, 127, 0.08) 0%, rgba(13, 138, 107, 0.12) 100%)';
            indicatorDiv.style.border = '2px solid rgba(16, 163, 127, 0.2)';
            indicatorDiv.style.borderRadius = '16px';
            indicatorDiv.style.backdropFilter = 'blur(10px)';
            indicatorDiv.style.boxShadow = '0 8px 32px rgba(16, 163, 127, 0.15)';

            contentDiv.appendChild(indicatorDiv);
            typingDiv.appendChild(avatarDiv);
            typingDiv.appendChild(contentDiv);
            messagesArea.appendChild(typingDiv);

            // Scroll to bottom with smooth animation
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.add('copied');

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.classList.remove('copied');
                }, 2000);
            });
        }

        function startNewChat(options = { clearCategory: true }) {
            // Use new chat manager
            chatManager.createNewChat();
            
            // Clear legacy variables
            chatHistory = [];
            currentChatId = null;
            if (options.clearCategory) {
                currentCategory = null;
            }

            // Clear input
            messageInput.value = '';
            updateSendButtonState();

            // Focus input
            messageInput.focus();
        }

        function selectCategory(category) {
            currentCategory = category;

            // Start new chat with category context but keep the category
            startNewChat({ clearCategory: false });

            // Add system message for category context
            const categoryMessages = {
                'problem-solving': 'وضع حل المسائل: اكتب المسألة مع المعطيات، وسأعطيك خطوات الحل والتحقق.',
                'sciences': 'وضع العلوم: اسأل عن تجربة، قانون، أو تفسير علمي وسأفصل بالإجابات.',
                'studies': 'وضع الدراسات: أرسل موضوعاً بحثياً وسأساعدك في صياغة خطة ومنهجية ومراجع.',
                'physics': 'وضع الفيزياء: أرسل المسألة وسأحلها بالقوانين المناسبة والخطوات والوحدات.',
                'philosophy': 'وضع الفلسفة: اطرح فكرة أو موقفاً وسأحلله بمنظور مدارس فكرية متنوعة.',
                'tech-info': 'وضع التقنية: اسأل عن تقنية/كود/أفضل ممارسة وسأقدم أمثلة وحلول.'
            };

            console.log('✅ تم تحميل جميع الأوضاع والإعدادات بنجاح');
        }
    </script>

    <!-- Simple Footer -->
    <div class="simple-footer">
        <p>تم التصميم بواسطة Mohamed Elashrafy 2025 - النظام تجريبي</p>
    </div>

    <style>
        .simple-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(13, 17, 23, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid var(--border-light);
            padding: 8px 16px;
            text-align: center;
            z-index: 100;
        }

        .simple-footer p {
            color: var(--text-secondary);
            font-size: 12px;
            margin: 0;
            opacity: 0.8;
        }

        /* Add padding to main content to avoid footer overlap */
        .main-content {
            padding-bottom: 40px;
        }

        /* Hide footer on mobile when keyboard is open */
        @media (max-width: 768px) {
            .simple-footer {
                display: none;
            }
        }
    </style>
</body>
</html>